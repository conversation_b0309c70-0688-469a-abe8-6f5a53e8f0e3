# Deployment Guide - Mixed Content Solution

## Problem
Your NocoDB instance runs on HTTP (`http://************:8080`) but your website needs to be served over HTTPS for security. This creates a "mixed content" error where browsers block HTTP requests from HTTPS pages.

## Solutions

### Option 1: Development (Current Setup)
✅ **Already configured** - Vite proxy handles this automatically in development mode.

When running `npm run dev`, requests to `/api/nocodb/*` are automatically proxied to your HTTP NocoDB instance.

### Option 2: Production - Server-Side Proxy (Recommended)

#### Using Nginx
Create an nginx configuration:

```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    # SSL configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # Serve your React app
    location / {
        root /path/to/your/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # Proxy NocoDB requests
    location /api/nocodb/ {
        proxy_pass http://************:8080/api/v2/tables/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### Using Apache
```apache
<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /path/to/your/dist
    
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    # Proxy NocoDB requests
    ProxyPass /api/nocodb/ http://************:8080/api/v2/tables/
    ProxyPassReverse /api/nocodb/ http://************:8080/api/v2/tables/
</VirtualHost>
```

### Option 3: Enable HTTPS on NocoDB (Best Long-term Solution)

1. **Set up SSL certificate** on your NocoDB server
2. **Configure NocoDB** to use HTTPS on port 443
3. **Update environment variable**:
   ```bash
   VITE_NOCODB_BASE_URL=https://************
   ```

### Option 4: Use Cloudflare or Similar CDN

1. **Point your domain** to Cloudflare
2. **Enable "Always Use HTTPS"** in Cloudflare
3. **Set up page rules** to proxy `/api/nocodb/*` to your HTTP NocoDB instance
4. **Update environment variable** to use your domain

## Current Configuration

The code automatically detects the environment and handles mixed content:

- **Development**: Uses Vite proxy (`/api/nocodb`)
- **Production**: Attempts to use proxy endpoint (you need to set this up)

## Testing

1. **Development**: Run `npm run dev` - should work automatically
2. **Production**: Deploy with one of the proxy solutions above

## Environment Variables

```bash
# Keep your current NocoDB URL
VITE_NOCODB_BASE_URL=http://************:8080

# Other required variables
VITE_NOCODB_API_TOKEN=your-nocodb-api-token
VITE_BREVO_API_KEY=your-brevo-api-key
VITE_ADMIN_EMAIL=<EMAIL>
```

## Troubleshooting

- Check browser console for detailed error messages
- Verify proxy configuration is working
- Test API endpoints directly
- Ensure CORS is properly configured on your NocoDB instance
