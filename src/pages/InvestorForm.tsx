import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import Header from '@/components/Header';
import RoleSelectionModal from '@/components/RoleSelectionModal';
import SuccessModal from '@/components/SuccessModal';
import {
  User,
  Mail,
  Linkedin,
  Building,
  Globe,
  Target,
  DollarSign,
  MapPin,
  TrendingUp,
  MessageSquare,
  CheckCircle,
  MessageCircle
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { nocoDBService } from '@/services/nocodb';
import { emailService } from '@/services/email';

interface InvestorFormData {
  investor_name: string;
  investor_email: string;
  in_mailinglist: boolean;
  investor_linkedin_url: string;
  firm_name: string;
  firm_website_url: string;
  investor_type: string;
  investment_stages: string[];
  preferred_verticals: string[];
  geography_preference: string[];
  average_check_size: string;
  thesis_keywords: string;
  source: string;
  investor_wechat_id_cn: string;
  investor_wechat_official_account: string;
  focus_on_chuhai_cn: boolean;
}

const InvestorForm = () => {
  const { t, i18n } = useTranslation();
  const isChineseVersion = i18n.language === 'zh';
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);

  const handleJoinNow = () => {
    setIsRoleModalOpen(true);
  };

  const [formData, setFormData] = useState<InvestorFormData>({
    investor_name: '',
    investor_email: '',
    in_mailinglist: false,
    investor_linkedin_url: '',
    firm_name: '',
    firm_website_url: '',
    investor_type: '',
    investment_stages: [],
    preferred_verticals: [],
    geography_preference: [],
    average_check_size: '',
    thesis_keywords: '',
    source: 'Investor Form',
    investor_wechat_id_cn: '',
    investor_wechat_official_account: '',
    focus_on_chuhai_cn: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const investorTypes = [
    'angel_investor',
    'venture_capital',
    'family_office',
    'accelerator',
    'incubator',
    'government_fund',
    'private_equity',
    'corporate_vc',
    'other'
  ];

  const investmentStages = [
    'idea',
    'mvp',
    'early_traction',
    'seed',
    'series_a',
    'series_b',
    'growth'
  ];

  const verticals = [
    'ai',
    'saas',
    'fintech',
    'biotech',
    'dev_tool',
    'social_media',
    'e_commerce',
    'edutech',
    'enterprise',
    'robotics',
    'hardware',
    'healthcare',
    'proptech',
    'cleantech',
    'mobility',
    'consumer',
    'blockchain',
    'cybersecurity',
    'entertainment'
  ];

  const geographyOptions = [
    'north_america',
    'europe',
    'china_mainland',
    'greater_china',
    'pan_asia'
  ];

  const checkSizeOptions = [
    '< $50k',
    '$50k - $250k',
    '$250k - $1M',
    '> $1M'
  ];

  const handleInputChange = (field: keyof InvestorFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayFieldChange = (field: 'investment_stages' | 'preferred_verticals' | 'geography_preference', value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...prev[field], value]
        : prev[field].filter(item => item !== value)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate required fields
    const requiredFields = [
      'investor_name',
      'investor_email',
      ...(isChineseVersion ? [] : ['investor_linkedin_url']), // LinkedIn only required for non-Chinese version
      'investor_type',
      'investment_stages',
      'preferred_verticals',
      'geography_preference',
      'average_check_size'
    ];

    for (const field of requiredFields) {
      // Check array fields
      if (field === 'investment_stages' || field === 'preferred_verticals' || field === 'geography_preference') {
        if (!formData[field] || formData[field].length === 0) {
          toast.error(`Please select at least one option for ${field.replace('_', ' ')}`);
          setIsSubmitting(false);
          return;
        }
      } else {
        // Check string fields
        if (!formData[field] || (typeof formData[field] === 'string' && formData[field].trim() === '')) {
          toast.error(`Please fill in the ${field.replace('_', ' ')} field`);
          setIsSubmitting(false);
          return;
        }
      }
    }

    try {
      // Submit to NocoDB
      console.log('Investor form submitted:', formData);

      const result = await nocoDBService.submitInvestorData(formData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to submit data');
      }

      // Send email notifications
      try {
        // Send admin notification
        await emailService.sendAdminNotification({
          type: 'investor',
          data: formData
        });

        // Send confirmation email to investor with language detection
        await emailService.sendConfirmationEmail(
          formData.investor_email,
          'investor',
          formData.investor_name,
          i18n.language as 'en' | 'zh'
        );
      } catch (emailError) {
        console.error('Email notification failed:', emailError);
        // Don't fail the form submission if email fails
      }

      setShowSuccessModal(true);

      // Reset form
      setFormData({
        investor_name: '',
        investor_email: '',
        in_mailinglist: false,
        investor_linkedin_url: '',
        firm_name: '',
        firm_website_url: '',
        investor_type: '',
        investment_stages: [],
        preferred_verticals: [],
        geography_preference: [],
        average_check_size: '',
        thesis_keywords: '',
        source: 'Investor Form',
        investor_wechat_id_cn: '',
        investor_wechat_official_account: '',
        focus_on_chuhai_cn: false,
      });
      
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header onJoinNowClick={handleJoinNow} />

      {/* Main Content */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-semibold text-gray-900 mb-6">{t('investorForm.pageTitle')}</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t('investorForm.pageSubtitle')}
            </p>
          </div>

          <Card className="bg-white shadow-2xl border-0 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] text-white p-8">
              <CardTitle className="text-3xl flex items-center justify-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-7 h-7" />
                </div>
                <span>{t('investorForm.title')}</span>
              </CardTitle>
              <p className="text-center text-white/90 text-lg mt-3">
                {t('investorForm.subtitle')}
              </p>
              <p className="text-center text-white/90 text-sm mt-4">
                <span className="text-red-300">*</span> {t('investorForm.required')}
              </p>
            </CardHeader>

          <CardContent className="p-12">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Personal Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <User className="w-5 h-5 text-[#40826D]" />
                  <span>{t('investorForm.personalInfo')}</span>
                </h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="investor_name" className="text-gray-700 font-medium">
                      {t('investorForm.fields.investorName')} <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="investor_name"
                      placeholder={t('investorForm.placeholders.investorName')}
                      value={formData.investor_name}
                      onChange={(e) => handleInputChange('investor_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="investor_email" className="text-gray-700 font-medium flex items-center space-x-1">
                        <Mail className="w-4 h-4" />
                        <span>{t('investorForm.fields.investorEmail')} <span className="text-red-500">*</span></span>
                      </Label>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="in_mailinglist"
                          checked={formData.in_mailinglist}
                          onCheckedChange={(checked) => handleInputChange('in_mailinglist', checked)}
                        />
                        <Label htmlFor="in_mailinglist" className="text-gray-700 font-medium cursor-pointer text-sm">
                          {t('investorForm.fields.inMailinglist')}
                        </Label>
                      </div>
                    </div>
                    <Input
                      id="investor_email"
                      type="email"
                      placeholder={t('investorForm.placeholders.investorEmail')}
                      value={formData.investor_email}
                      onChange={(e) => handleInputChange('investor_email', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="investor_linkedin_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Linkedin className="w-4 h-4" />
                      <span>
                        {t('investorForm.fields.investorLinkedin')}
                        {isChineseVersion ? (
                          <span className="text-gray-500"> {t('form.optional')}</span>
                        ) : (
                          <span className="text-red-500"> *</span>
                        )}
                      </span>
                    </Label>
                    <Input
                      id="investor_linkedin_url"
                      type="url"
                      value={formData.investor_linkedin_url}
                      onChange={(e) => handleInputChange('investor_linkedin_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('investorForm.placeholders.investorLinkedin')}
                      required={!isChineseVersion}
                    />
                  </div>
                </div>
              </div>

              {/* Firm Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Building className="w-5 h-5 text-[#40826D]" />
                  <span>{t('investorForm.firmInfo')}</span>
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="firm_name" className="text-gray-700 font-medium">
                      {t('investorForm.fields.firmName')}
                    </Label>
                    <Input
                      id="firm_name"
                      placeholder={t('investorForm.placeholders.firmName')}
                      value={formData.firm_name}
                      onChange={(e) => handleInputChange('firm_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="firm_website_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>{t('investorForm.fields.firmWebsite')}</span>
                    </Label>
                    <Input
                      id="firm_website_url"
                      type="url"
                      value={formData.firm_website_url}
                      onChange={(e) => handleInputChange('firm_website_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('investorForm.placeholders.firmWebsite')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="investor_type" className="text-gray-700 font-medium">
                      {t('investorForm.fields.investorType')} <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.investor_type} onValueChange={(value) => handleInputChange('investor_type', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder={t('investorForm.fields.investorType')} />
                      </SelectTrigger>
                      <SelectContent>
                        {investorTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {t(`investorForm.investorTypes.${type}`)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Investment Preferences */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Target className="w-5 h-5 text-[#40826D]" />
                  <span>{t('investorForm.investmentPreferences')}</span>
                </h3>

                <div className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-gray-700 font-medium">{t('investorForm.fields.investmentStages')} <span className="text-red-500">*</span></Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {investmentStages.map((stage) => (
                        <div key={stage} className="flex items-center space-x-2">
                          <Checkbox
                            id={`stage-${stage}`}
                            checked={formData.investment_stages.includes(stage)}
                            onCheckedChange={(checked) =>
                              handleArrayFieldChange('investment_stages', stage, checked as boolean)
                            }
                          />
                          <Label htmlFor={`stage-${stage}`} className="text-sm text-gray-600">
                            {t(`investorForm.investmentStages.${stage}`)}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-gray-700 font-medium">{t('investorForm.fields.preferredVerticals')} <span className="text-red-500">*</span></Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {verticals.map((vertical) => (
                        <div key={vertical} className="flex items-center space-x-2">
                          <Checkbox
                            id={`vertical-${vertical}`}
                            checked={formData.preferred_verticals.includes(vertical)}
                            onCheckedChange={(checked) =>
                              handleArrayFieldChange('preferred_verticals', vertical, checked as boolean)
                            }
                          />
                          <Label htmlFor={`vertical-${vertical}`} className="text-sm text-gray-600">
                            {t(`investorForm.verticals.${vertical}`)}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label className="text-gray-700 font-medium flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{t('investorForm.fields.geographyPreference')} <span className="text-red-500">*</span></span>
                      </Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {geographyOptions.map((geo) => (
                          <div key={geo} className="flex items-center space-x-2">
                            <Checkbox
                              id={`geo-${geo}`}
                              checked={formData.geography_preference.includes(geo)}
                              onCheckedChange={(checked) =>
                                handleArrayFieldChange('geography_preference', geo, checked as boolean)
                              }
                            />
                            <Label htmlFor={`geo-${geo}`} className="text-sm text-gray-600">
                              {t(`investorForm.geographyOptions.${geo}`)}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="average_check_size" className="text-gray-700 font-medium flex items-center space-x-1">
                        <DollarSign className="w-4 h-4" />
                        <span>{t('investorForm.fields.averageCheckSize')} <span className="text-red-500">*</span></span>
                      </Label>
                      <Select value={formData.average_check_size} onValueChange={(value) => handleInputChange('average_check_size', value)}>
                        <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                          <SelectValue placeholder={t('investorForm.fields.averageCheckSize')} />
                        </SelectTrigger>
                        <SelectContent>
                          {checkSizeOptions.map((size) => (
                            <SelectItem key={size} value={size}>
                              {t(`investorForm.checkSizeOptions.${size}`)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="thesis_keywords" className="text-gray-700 font-medium flex items-center space-x-1">
                      <MessageSquare className="w-4 h-4" />
                      <span>{t('investorForm.fields.thesisKeywords')}</span>
                    </Label>
                    <Textarea
                      id="thesis_keywords"
                      placeholder={t('investorForm.placeholders.thesisKeywords')}
                      value={formData.thesis_keywords}
                      onChange={(e) => handleInputChange('thesis_keywords', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      rows={4}
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              {isChineseVersion && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-[#40826D]" />
                    <span>{t('investorForm.additionalInfo')}</span>
                  </h3>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="investor_wechat_id_cn" className="text-gray-700 font-medium flex items-center space-x-1">
                        <MessageCircle className="w-4 h-4" />
                        <span>{t('investorForm.fields.investorWechatId')} (仅用于与您联系，不公开)</span>
                      </Label>
                      <Input
                        id="investor_wechat_id_cn"
                        value={formData.investor_wechat_id_cn}
                        onChange={(e) => handleInputChange('investor_wechat_id_cn', e.target.value)}
                        className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                        placeholder={t('placeholder.wechatId')}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="investor_wechat_official_account" className="text-gray-700 font-medium">
                        {t('investorForm.fields.investorWechatOfficialAccount')}
                      </Label>
                      <Input
                        id="investor_wechat_official_account"
                        value={formData.investor_wechat_official_account}
                        onChange={(e) => handleInputChange('investor_wechat_official_account', e.target.value)}
                        className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                        placeholder={t('placeholder.projectWechatAccount')}
                      />
                    </div>

                    <div className="flex items-center space-x-3 py-4">
                      <Switch
                        id="focus_on_chuhai_cn"
                        checked={formData.focus_on_chuhai_cn}
                        onCheckedChange={(checked) => handleInputChange('focus_on_chuhai_cn', checked)}
                        className="data-[state=checked]:bg-[#40826D]"
                      />
                      <Label htmlFor="focus_on_chuhai_cn" className="text-gray-700 font-medium cursor-pointer">
                        {t('investorForm.fields.focusOnChuhai')}
                      </Label>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div className="flex flex-col items-center pt-8 space-y-4">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white px-12 py-3 text-lg font-semibold"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {t('investorForm.submitting')}
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-5 h-5 mr-2" />
                      {t('investorForm.submit')}
                    </>
                  )}
                </Button>

                <div className="text-sm text-gray-600 text-center">
                  {t('form.agreementText')}{' '}
                  <a href="/privacy-policy" className="text-[#40826D] hover:underline" target="_blank" rel="noopener noreferrer">
                    {t('footer.privacyPolicy')}
                  </a>{' '}
                  {t('form.and')}{' '}
                  <a href="/terms-of-service" className="text-[#40826D] hover:underline" target="_blank" rel="noopener noreferrer">
                    {t('footer.termsOfService')}
                  </a>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
        </div>
      </section>

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        type="investor"
      />

      {/* Role Selection Modal */}
      <RoleSelectionModal
        isOpen={isRoleModalOpen}
        onClose={() => setIsRoleModalOpen(false)}
      />
    </div>
  );
};

export default InvestorForm;
