
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Header from '@/components/Header';
import ProductDemo from '@/components/ProductDemo';
import RoleSelectionModal from '@/components/RoleSelectionModal';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { Button } from '@/components/ui/button';

import { Mail, Phone, MapPin, Github, Twitter, Linkedin, Star, TrendingUp, Shield, Brain, BarChart3, Users, Eye, Lightbulb, Target } from 'lucide-react';

const Index = () => {
  const { t } = useTranslation();
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);

  const handleJoinUs = () => {
    setIsRoleModalOpen(true);
  };

  const handleStartDiscovering = () => {
    // Scroll to contact section
    const contactSection = document.querySelector('footer');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header onJoinNowClick={handleJoinUs} />

      {/* Hero Section - Extended Height */}
      <section className="min-h-screen flex items-center py-32 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="space-y-12">
              <div className="space-y-8">
                <h1 className="text-6xl md:text-7xl font-bold text-gray-900 leading-tight">
                  {t('homepage.hero.title')}
                  <br />
                  <span className="text-[#40826D]">{t('homepage.hero.titleHighlight')}</span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                  {t('homepage.hero.subtitle')}
                </p>

                {/* Value Propositions */}
                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-[#40826D] rounded-full flex items-center justify-center">
                      <Eye className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl text-gray-700 font-medium">{t('homepage.features.aiIntelligence.title')}</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-[#40826D] rounded-full flex items-center justify-center">
                      <Target className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl text-gray-700 font-medium">{t('homepage.features.smartMatching.title')}</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-[#40826D] rounded-full flex items-center justify-center">
                      <TrendingUp className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl text-gray-700 font-medium">{t('homepage.features.realTimeData.title')}</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-[#40826D] to-[#008C8C] hover:from-[#40826D]/90 hover:to-[#008C8C]/90 text-white px-10 py-5 text-xl font-semibold rounded-lg shadow-lg"
                  onClick={handleJoinUs}
                >
                  {t('homepage.hero.getStarted')}
                </Button>
              </div>
            </div>

            {/* Right Content - Dashboard Preview */}
            <div className="relative">
              <div className="relative">
                <img
                  src="/lovable-uploads/aca6c8ae-cb9d-4581-9fb5-ac2c4c13184b.png"
                  alt="Investment Dashboard Interface"
                  className="w-full h-auto rounded-2xl shadow-2xl"
                />
                {/* Floating elements for visual interest */}
                <div className="absolute -top-4 -right-4 w-20 h-20 bg-[#40826D]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-[#008C8C]/10 rounded-full blur-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              {t('homepage.features.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {t('homepage.features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature Card 1 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('homepage.features.aiFounderAnalysis.title')}</h3>
              <p className="text-gray-600 leading-relaxed">
                {t('homepage.features.aiFounderAnalysis.description')}
              </p>
            </div>

            {/* Feature Card 2 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('homepage.features.marketIntelligence.title')}</h3>
              <p className="text-gray-600 leading-relaxed">
                {t('homepage.features.marketIntelligence.description')}
              </p>
            </div>

            {/* Feature Card 3 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('homepage.features.riskAssessment.title')}</h3>
              <p className="text-gray-600 leading-relaxed">
                {t('homepage.features.riskAssessment.description')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Clean Footer */}
      <footer className="relative py-12 overflow-hidden">
        {/* Metallic viridian gradient base */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a2f2a] via-[#40826D] to-[#0d1b17]"></div>
        
        {/* Wave ripple effects */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00ffaa]/15 to-transparent animate-[wave_6s_ease-in-out_infinite]"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00d4aa]/10 to-transparent animate-[wave_6s_ease-in-out_infinite_2s]"></div>
        </div>
        
        {/* Metallic shine overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/3 to-transparent animate-[shimmer_8s_ease-in-out_infinite]"></div>
        
        {/* Crystal patterns */}
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: `radial-gradient(circle at 30% 70%, #00ffaa 0%, transparent 50%), 
                           radial-gradient(circle at 70% 30%, #00d4aa 0%, transparent 50%)`
        }}></div>
        
        <div className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 flex items-center justify-center">
                  <img
                    src="/logo.png"
                    alt="Veridian Vista Logo"
                    className="w-8 h-8 object-contain drop-shadow-lg"
                    onError={(e) => {
                      // Fallback to a simple text logo if image fails to load
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.parentElement!.innerHTML = '<div class="text-white font-bold text-lg">VV</div>';
                    }}
                  />
                </div>
                <h3 className="text-2xl font-bold text-white">Veridian Vista</h3>
              </div>
              <p className="text-white/80 mb-6 max-w-md">
                {t('footer.description')}
              </p>
              {/* <div className="flex space-x-4">
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Twitter className="w-6 h-6" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Linkedin className="w-6 h-6" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Github className="w-6 h-6" />
                </a>
              </div> */}
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">{t('footer.product')}</h4>
              <ul className="space-y-3">
                <li>
                  <a href="/investor" className="text-white/80 hover:text-white transition-colors">
                    {t('footer.forInvestors')}
                  </a>
                </li>
                <li>
                  <a href="/founder" className="text-white/80 hover:text-white transition-colors">
                    {t('footer.forFounders')}
                  </a>
                </li>
                <li>
                  <a href="/about" className="text-white/80 hover:text-white transition-colors">
                    {t('footer.aboutUs')}
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">{t('footer.contact')}</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-white" />
                  <span className="text-white/80"><EMAIL></span>
                </div>
                {/* <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-[#40826D]" />
                  <span className="text-gray-600">+****************</span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="w-4 h-4 text-[#40826D]" />
                  <span className="text-gray-600">Palo Alto, CA</span>
                </div> */}
              </div>
            </div>
          </div>

          <div className="border-t border-white/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-white/60 text-sm mb-4 md:mb-0">
                © 2025 Veridian Vista. All rights reserved.
              </p>
              <div className="flex space-x-6 text-sm">
                <a href="/privacy-policy" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.privacyPolicy')}
                </a>
                <a href="/terms-of-service" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.termsOfService')}
                </a>
              </div>
            </div>
          </div>
          </div>
        </div>
      </footer>

      {/* Role Selection Modal */}
      <RoleSelectionModal
        isOpen={isRoleModalOpen}
        onClose={() => setIsRoleModalOpen(false)}
      />
    </div>
  );
};

export default Index;
