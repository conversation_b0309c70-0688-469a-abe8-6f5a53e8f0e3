// NocoDB API service
const NOCODB_BASE_URL = import.meta.env.VITE_NOCODB_BASE_URL;
const NOCODB_API_TOKEN = import.meta.env.VITE_NOCODB_API_TOKEN;

// Base and table names
const NOCODB_BASE_NAME = 'VeridianVista';
const FOUNDERS_TABLE_NAME = 'Founders';
const INVESTORS_TABLE_NAME = 'Investors';

interface NocoDBResponse {
  success: boolean;
  data?: any;
  error?: string;
}

class NocoDBService {
  private baseURL: string;
  private headers: HeadersInit;

  constructor() {
    // Handle mixed content issues when NocoDB is HTTP but site is HTTPS
    let baseUrl = NOCODB_BASE_URL;
    const isProduction = window.location.protocol === 'https:';
    const isDevelopment = import.meta.env.DEV;
    const isNocoDBHttp = baseUrl && baseUrl.startsWith('http://');

    if (isProduction && isNocoDBHttp) {
      console.warn('⚠️ Mixed content detected: HTTPS site trying to access HTTP NocoDB');

      if (isDevelopment) {
        // Use Vite's proxy in development
        console.log('🔧 Using Vite proxy to handle mixed content in development');
        baseUrl = '/api/nocodb';
      } else {
        // For production, you'll need a server-side proxy
        console.warn('🚨 Production mixed content issue! You need to:');
        console.warn('1. Enable HTTPS on your NocoDB instance, OR');
        console.warn('2. Set up a server-side proxy, OR');
        console.warn('3. Deploy your frontend on HTTP (not recommended)');

        // Fallback: try to use relative proxy path (if you set one up)
        baseUrl = '/api/nocodb';
      }
    }

    // Determine the final API URL
    if (baseUrl.startsWith('/api/nocodb')) {
      // Using proxy - don't add /api/v2/tables as it's handled by proxy rewrite
      this.baseURL = baseUrl;
    } else {
      // Direct connection
      this.baseURL = `${baseUrl}/api/v2/tables`;
    }

    this.headers = {
      'Content-Type': 'application/json',
      'xc-token': NOCODB_API_TOKEN,
    };

    console.log('NocoDB Service initialized with URL:', this.baseURL);
    console.log('Environment:', isDevelopment ? 'Development' : 'Production');
  }

  private getTableId(tableName: string): string {
    // In NocoDB, you can use table names directly in the API
    // or get the table ID from the base metadata
    return tableName;
  }

  async submitFounderData(data: any): Promise<NocoDBResponse> {
    try {
      // Handle both direct and proxy URLs
      const url = `${this.baseURL}/${this.getTableId(FOUNDERS_TABLE_NAME)}/records`;
      console.log('Submitting founder data to:', url);

      const response = await fetch(url, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('NocoDB API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('Founder data submitted successfully:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('Error submitting founder data:', error);

      // Provide more specific error messages
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        return {
          success: false,
          error: 'Network error: Unable to connect to database. Please check your internet connection or contact support.'
        };
      }

      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async submitInvestorData(data: any): Promise<NocoDBResponse> {
    try {
      const url = `${this.baseURL}/${this.getTableId(INVESTORS_TABLE_NAME)}/records`;
      console.log('Submitting investor data to:', url);

      const response = await fetch(url, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('NocoDB API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('Investor data submitted successfully:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('Error submitting investor data:', error);

      // Provide more specific error messages
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        return {
          success: false,
          error: 'Network error: Unable to connect to database. Please check your internet connection or contact support.'
        };
      }

      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getFounders(limit = 50, offset = 0): Promise<NocoDBResponse> {
    try {
      const response = await fetch(
        `${this.baseURL}/${this.getTableId(FOUNDERS_TABLE_NAME)}/records?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: this.headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error fetching founders:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getInvestors(limit = 50, offset = 0): Promise<NocoDBResponse> {
    try {
      const response = await fetch(
        `${this.baseURL}/${this.getTableId(INVESTORS_TABLE_NAME)}/records?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: this.headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error fetching investors:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

export const nocoDBService = new NocoDBService();
export default NocoDBService;
