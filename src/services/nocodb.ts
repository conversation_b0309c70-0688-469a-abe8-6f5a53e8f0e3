// NocoDB API service
const NOCODB_BASE_URL = import.meta.env.VITE_NOCODB_BASE_URL;
const NOCODB_API_TOKEN = import.meta.env.VITE_NOCODB_API_TOKEN;

// Base and table configuration
const NOCODB_BASE_NAME = 'VeridianVista';
// Table IDs from your NocoDB instance
const FOUNDERS_TABLE_ID = 'mc8jcg6pwoe54mc';
const INVESTORS_TABLE_ID = 'mnbsluckjtqp8ko';

interface NocoDBResponse {
  success: boolean;
  data?: any;
  error?: string;
}

class NocoDBService {
  private baseURL: string;
  private headers: HeadersInit;

  constructor() {
    // Check if environment variables are properly configured
    if (!NOCODB_BASE_URL) {
      console.error('❌ VITE_NOCODB_BASE_URL is not configured!');
      console.error('Please set VITE_NOCODB_BASE_URL in your .env file');
      throw new Error('NocoDB base URL is not configured');
    }

    if (!NOCODB_API_TOKEN) {
      console.error('❌ VITE_NOCODB_API_TOKEN is not configured!');
      console.error('Please set VITE_NOCODB_API_TOKEN in your .env file');
      throw new Error('NocoDB API token is not configured');
    }

    // Handle mixed content issues when NocoDB is HTTP but site is HTTPS
    let baseUrl = NOCODB_BASE_URL;
    const isProduction = window.location.protocol === 'https:';
    const isDevelopment = import.meta.env.DEV;
    const isNocoDBHttp = baseUrl.startsWith('http://');

    if (isProduction && isNocoDBHttp) {
      console.warn('⚠️ Mixed content detected: HTTPS site trying to access HTTP NocoDB');

      if (isDevelopment) {
        // Use Vite's proxy in development
        console.log('🔧 Using Vite proxy to handle mixed content in development');
        baseUrl = '/api/nocodb';
      } else {
        // For production, you'll need a server-side proxy
        console.warn('🚨 Production mixed content issue! You need to:');
        console.warn('1. Enable HTTPS on your NocoDB instance, OR');
        console.warn('2. Set up a server-side proxy, OR');
        console.warn('3. Deploy your frontend on HTTP (not recommended)');

        // Fallback: try to use relative proxy path (if you set one up)
        baseUrl = '/api/nocodb';
      }
    }

    // Determine the final API URL
    if (baseUrl.startsWith('/api/nocodb')) {
      // Using proxy - don't add /api/v2/tables as it's handled by proxy rewrite
      this.baseURL = baseUrl;
    } else {
      // Direct connection - use the correct NocoDB API format
      this.baseURL = `${baseUrl}/api/v2/tables`;
    }

    this.headers = {
      'Content-Type': 'application/json',
      'xc-token': NOCODB_API_TOKEN,
    };

    console.log('NocoDB Service initialized with URL:', this.baseURL);
    console.log('Environment:', isDevelopment ? 'Development' : 'Production');
    console.log('Base URL from env:', NOCODB_BASE_URL);
    console.log('Expected base name:', NOCODB_BASE_NAME);
    console.log('Expected table IDs:', FOUNDERS_TABLE_ID, INVESTORS_TABLE_ID);

    // Test connection in development
    if (isDevelopment) {
      this.testConnection();
    }
  }

  private getTableId(tableType: 'founder' | 'investor'): string {
    // Return the actual table ID from your NocoDB instance
    return tableType === 'founder' ? FOUNDERS_TABLE_ID : INVESTORS_TABLE_ID;
  }

  private async testConnection(): Promise<boolean> {
    try {
      // Test connection by trying to fetch table list
      const testUrl = this.baseURL.includes('/api/nocodb')
        ? `/api/nocodb/${FOUNDERS_TABLE_ID}`
        : `${this.baseURL}/${FOUNDERS_TABLE_ID}`;

      console.log('🔍 Testing NocoDB connection to:', testUrl);

      const response = await fetch(testUrl, {
        method: 'GET',
        headers: this.headers,
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ NocoDB connection successful! Sample data:', data);
        return true;
      } else {
        const errorText = await response.text();
        console.error('❌ NocoDB connection failed:', response.status, errorText);

        // Try alternative API endpoints
        console.log('🔄 Trying alternative API endpoints...');
        await this.tryAlternativeEndpoints();
        return false;
      }
    } catch (error) {
      console.error('❌ NocoDB connection test failed:', error);
      return false;
    }
  }

  private async tryAlternativeEndpoints(): Promise<void> {
    const alternatives = [
      `${NOCODB_BASE_URL}/api/v1/db/data/noco/${NOCODB_BASE_NAME}/${FOUNDERS_TABLE_ID}`,
      `${NOCODB_BASE_URL}/api/v1/db/data/${NOCODB_BASE_NAME}/${FOUNDERS_TABLE_ID}`,
      `${NOCODB_BASE_URL}/api/v2/tables/${FOUNDERS_TABLE_ID}/records`,
      `${NOCODB_BASE_URL}/nc/${NOCODB_BASE_NAME}/api/v1/${FOUNDERS_TABLE_ID}`,
    ];

    for (const url of alternatives) {
      try {
        console.log('🧪 Testing alternative endpoint:', url);
        const response = await fetch(url, {
          method: 'GET',
          headers: this.headers,
        });

        if (response.ok) {
          console.log('✅ Alternative endpoint works:', url);
          console.log('💡 Consider updating your API configuration to use this endpoint');
          break;
        } else {
          console.log('❌ Alternative endpoint failed:', response.status);
        }
      } catch (error) {
        console.log('❌ Alternative endpoint error:', error);
      }
    }
  }

  async submitFounderData(data: any): Promise<NocoDBResponse> {
    try {
      // Handle both direct and proxy URLs
      const url = `${this.baseURL}/${this.getTableId('founder')}/records`;
      console.log('🚀 Submitting founder data to:', url);
      console.log('📊 Data being submitted:', data);
      console.log('🔑 Headers:', this.headers);

      const response = await fetch(url, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('NocoDB API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('Founder data submitted successfully:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('Error submitting founder data:', error);

      // Provide more specific error messages
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        return {
          success: false,
          error: 'Network error: Unable to connect to database. Please check your internet connection or contact support.'
        };
      }

      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async submitInvestorData(data: any): Promise<NocoDBResponse> {
    try {
      const url = `${this.baseURL}/${this.getTableId('investor')}/records`;
      console.log('Submitting investor data to:', url);

      const response = await fetch(url, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('NocoDB API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('Investor data submitted successfully:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('Error submitting investor data:', error);

      // Provide more specific error messages
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        return {
          success: false,
          error: 'Network error: Unable to connect to database. Please check your internet connection or contact support.'
        };
      }

      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getFounders(limit = 50, offset = 0): Promise<NocoDBResponse> {
    try {
      const response = await fetch(
        `${this.baseURL}/${this.getTableId('founder')}/records?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: this.headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error fetching founders:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getInvestors(limit = 50, offset = 0): Promise<NocoDBResponse> {
    try {
      const response = await fetch(
        `${this.baseURL}/${this.getTableId('investor')}/records?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: this.headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error fetching investors:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

export const nocoDBService = new NocoDBService();
export default NocoDBService;
