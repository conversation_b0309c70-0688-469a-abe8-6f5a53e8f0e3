// Email service using Brevo (formerly SendinBlue)
const BREVO_API_KEY = import.meta.env.VITE_BREVO_API_KEY;
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email';
const ADMIN_EMAIL = import.meta.env.VITE_ADMIN_EMAIL;

interface EmailData {
  to: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
}

interface NotificationEmailData {
  type: 'founder' | 'investor';
  data: any;
}

class EmailService {
  private apiKey: string;
  private apiUrl: string;

  constructor() {
    this.apiKey = BREVO_API_KEY;
    this.apiUrl = BREVO_API_URL;
  }

  private async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': this.apiKey,
        },
        body: JSON.stringify({
          sender: {
            name: 'Veridian Vista',
            email: ADMIN_EMAIL,
          },
          to: [
            {
              email: emailData.to,
            },
          ],
          subject: emailData.subject,
          htmlContent: emailData.htmlContent,
          textContent: emailData.textContent || emailData.htmlContent.replace(/<[^>]*>/g, ''),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  async sendAdminNotification(notificationData: NotificationEmailData): Promise<boolean> {
    const { type, data } = notificationData;
    
    const subject = `New ${type === 'founder' ? 'Founder' : 'Investor'} Registration - ${data.name || data.founder_name || data.investor_name}`;
    
    const htmlContent = `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background-color: #40826D; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .data-section { margin: 20px 0; }
            .data-section h3 { color: #40826D; border-bottom: 2px solid #40826D; padding-bottom: 5px; }
            .data-item { margin: 10px 0; }
            .data-label { font-weight: bold; color: #555; }
            .json-data { background-color: #f5f5f5; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>New ${type === 'founder' ? 'Founder' : 'Investor'} Registration</h1>
          </div>
          <div class="content">
            <p>A new ${type} has registered on Veridian Vista. Here are the details:</p>
            
            ${type === 'founder' ? this.formatFounderData(data) : this.formatInvestorData(data)}
            
            <div class="data-section">
              <h3>Complete JSON Data</h3>
              <div class="json-data">${JSON.stringify(data, null, 2)}</div>
            </div>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: ADMIN_EMAIL,
      subject,
      htmlContent,
    });
  }

  async sendConfirmationEmail(email: string, type: 'founder' | 'investor', name: string, language: 'en' | 'zh' = 'en'): Promise<boolean> {
    try {
      // Hardcoded Brevo template IDs based on your templates:
      // founder_zh = #1, founder_en = #2, investor_en = #4, investor_zh = #5
      const getTemplateId = (userType: 'founder' | 'investor', lang: 'en' | 'zh'): number => {
        if (userType === 'founder') {
          return lang === 'zh' ? 1 : 2; // founder_zh = 1, founder_en = 2
        } else {
          return lang === 'zh' ? 5 : 4; // investor_zh = 5, investor_en = 4
        }
      };

      // Extract first name from full name for template
      const getFirstName = (fullName: string): string => {
        if (!fullName) return 'there';

        // For Chinese names, typically the first character is the surname
        // and the rest are given names, but we'll use a simple approach
        const nameParts = fullName.trim().split(/\s+/);

        if (nameParts.length === 1) {
          // Single name - could be Chinese name or single word
          // For Chinese names like "张伟", we'll take the second character as first name
          // For English names, we'll use the whole name
          if (/[\u4e00-\u9fff]/.test(fullName)) {
            // Contains Chinese characters
            return fullName.length > 1 ? fullName.substring(1) : fullName;
          } else {
            // English single name
            return fullName;
          }
        } else {
          // Multiple parts - take the first part for English names
          // For Chinese names with spaces, take the second part
          if (/[\u4e00-\u9fff]/.test(fullName)) {
            // Contains Chinese characters - take second part if available
            return nameParts[1] || nameParts[0];
          } else {
            // English name - take first part
            return nameParts[0];
          }
        }
      };

      const firstName = getFirstName(name);
      const templateId = getTemplateId(type, language);

      console.log(`📧 Sending ${language} confirmation email to ${email}`);
      console.log(`👤 Full name: "${name}" → First name: "${firstName}"`);
      console.log(`📋 Using template ID: ${templateId}`);

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': this.apiKey,
        },
        body: JSON.stringify({
          sender: {
            name: 'VeridianVista Team',
            email: ADMIN_EMAIL,
          },
          to: [
            {
              email: email,
              name: name,
            },
          ],
          templateId: templateId,
          params: {
            // Match the template variable structure: {{ contact.FIRSTNAME }}
            contact: {
              FIRSTNAME: firstName,
            },
          },
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      console.log(`Confirmation email sent using template ${templateId} (${type}_${language}) to ${email}`);
      return true;
    } catch (error) {
      console.error('Error sending confirmation email:', error);
      return false;
    }
  }

  private formatFounderData(data: any): string {
    return `
      <div class="data-section">
        <h3>Personal Information</h3>
        <div class="data-item"><span class="data-label">Name:</span> ${data.founder_name || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Email:</span> ${data.founder_email || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Mailing List:</span> ${data.in_mailinglist || 'N/A'}</div>
        <div class="data-item"><span class="data-label">LinkedIn:</span> ${data.founder_linkedin_url || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Location:</span> ${data.founder_location || 'N/A'}</div>
      </div>
      
      <div class="data-section">
        <h3>Project Information</h3>
        <div class="data-item"><span class="data-label">Project Name:</span> ${data.project_name || 'N/A'}</div>
        <div class="data-item"><span class="data-label">One-liner:</span> ${data.project_oneliner || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Stage:</span> ${data.project_stage || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Industry Verticals:</span> ${Array.isArray(data.industry_verticals) ? data.industry_verticals.join(', ') : 'N/A'}</div>
        <div class="data-item"><span class="data-label">Funding Needs:</span> ${data.funding_needs || 'N/A'}</div>
      </div>
      
      ${data.founder_wechat_id_cn ? `
        <div class="data-section">
          <h3>Chinese Market Information</h3>
          <div class="data-item"><span class="data-label">WeChat ID:</span> ${data.founder_wechat_id_cn}</div>
          <div class="data-item"><span class="data-label">WeChat Group:</span> ${data.in_wechat_group || 'N/A'}</div>
        </div>
      ` : ''}
    `;
  }

  private formatInvestorData(data: any): string {
    return `
      <div class="data-section">
        <h3>Personal Information</h3>
        <div class="data-item"><span class="data-label">Name:</span> ${data.investor_name || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Email:</span> ${data.investor_email || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Mailing List:</span> ${data.in_mailinglist || 'N/A'}</div>
        <div class="data-item"><span class="data-label">LinkedIn:</span> ${data.investor_linkedin_url || 'N/A'}</div>
      </div>
      
      <div class="data-section">
        <h3>Firm Information</h3>
        <div class="data-item"><span class="data-label">Firm Name:</span> ${data.firm_name || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Firm Website:</span> ${data.firm_website_url || 'N/A'}</div>
        <div class="data-item"><span class="data-label">Investor Type:</span> ${data.investor_type || 'N/A'}</div>
      </div>
      
      <div class="data-section">
        <h3>Investment Preferences</h3>
        <div class="data-item"><span class="data-label">Investment Stages:</span> ${Array.isArray(data.investment_stages) ? data.investment_stages.join(', ') : 'N/A'}</div>
        <div class="data-item"><span class="data-label">Preferred Verticals:</span> ${Array.isArray(data.preferred_verticals) ? data.preferred_verticals.join(', ') : 'N/A'}</div>
        <div class="data-item"><span class="data-label">Geography:</span> ${Array.isArray(data.geography_preference) ? data.geography_preference.join(', ') : 'N/A'}</div>
        <div class="data-item"><span class="data-label">Check Size:</span> ${data.average_check_size || 'N/A'}</div>
      </div>
    `;
  }
}

export const emailService = new EmailService();
export default EmailService;

// Test function for first name extraction
export const testFirstNameExtraction = () => {
  const testCases = [
    { input: '张伟', expected: '伟' },
    { input: '李明', expected: '明' },
    { input: 'John Smith', expected: 'John' },
    { input: 'Sarah', expected: 'Sarah' },
    { input: '王小明', expected: '小明' },
    { input: 'Jean-Pierre Dupont', expected: 'Jean-Pierre' },
    { input: '', expected: 'there' },
  ];

  console.group('🧪 Testing First Name Extraction');

  testCases.forEach(({ input, expected }) => {
    const getFirstName = (fullName: string): string => {
      if (!fullName) return 'there';

      const nameParts = fullName.trim().split(/\s+/);

      if (nameParts.length === 1) {
        if (/[\u4e00-\u9fff]/.test(fullName)) {
          return fullName.length > 1 ? fullName.substring(1) : fullName;
        } else {
          return fullName;
        }
      } else {
        if (/[\u4e00-\u9fff]/.test(fullName)) {
          return nameParts[1] || nameParts[0];
        } else {
          return nameParts[0];
        }
      }
    };

    const result = getFirstName(input);
    const status = result === expected ? '✅' : '❌';
    console.log(`${status} "${input}" → "${result}" (expected: "${expected}")`);
  });

  console.groupEnd();
};
