// Test submission utilities for founder and investor forms
import { nocoDBService } from '@/services/nocodb';
import { emailService, testFirstNameExtraction } from '@/services/email';

export interface TestFounderData {
  founder_name: string;
  founder_email: string;
  in_mailinglist: boolean;
  founder_linkedin_url: string;
  founder_github_url: string;
  founder_twitter_url: string;
  founder_location: string;
  project_name: string;
  project_website_url: string;
  project_oneliner: string;
  project_description: string;
  project_stage: string;
  industry_verticals: string[];
  current_traction_data: string;
  funding_needs: string;
  source: string;
  founder_wechat_id_cn: string;
  in_wechat_group: boolean;
  project_wechat_official_account_cn: string;
  project_showcase_url_cn: string;
  developer_community_url_cn: string;
}

export interface TestInvestorData {
  investor_name: string;
  investor_email: string;
  in_mailinglist: boolean;
  investor_linkedin_url: string;
  firm_name: string;
  firm_website_url: string;
  investor_type: string;
  investment_stages: string[];
  preferred_verticals: string[];
  geography_preference: string[];
  average_check_size: string;
  thesis_keywords: string;
  source: string;
  investor_wechat_id_cn: string;
  investor_wechat_official_account: string;
  focus_on_chuhai_cn: boolean;
}

// Test data for founder form
export const testFounderData: TestFounderData = {
  founder_name: "张伟",
  founder_email: "<EMAIL>",
  in_mailinglist: true,
  founder_linkedin_url: "https://linkedin.com/in/zhangwei",
  founder_github_url: "https://github.com/zhangwei",
  founder_twitter_url: "https://twitter.com/zhangwei",
  founder_location: "北京, 中国",
  project_name: "智能医疗助手",
  project_website_url: "https://smartmedical.ai",
  project_oneliner: "基于AI的智能医疗诊断助手，帮助医生提高诊断准确率",
  project_description: "我们开发了一个基于深度学习的医疗诊断助手，能够分析医学影像和患者症状，为医生提供准确的诊断建议。我们的AI模型在多个医学数据集上达到了95%以上的准确率，已经在3家三甲医院进行试点应用。",
  project_stage: "early_traction",
  industry_verticals: ["ai", "healthcare", "enterprise"],
  current_traction_data: "已获得3家三甲医院试点合作，月活跃医生用户200+，诊断准确率提升15%",
  funding_needs: "raising_funds",
  source: "Founder Form",
  founder_wechat_id_cn: "zhangwei_ai",
  in_wechat_group: true,
  project_wechat_official_account_cn: "智能医疗助手",
  project_showcase_url_cn: "https://smartmedical.cn",
  developer_community_url_cn: "https://dev.smartmedical.cn"
};

// Test data for investor form
export const testInvestorData: TestInvestorData = {
  investor_name: "李明",
  investor_email: "<EMAIL>",
  in_mailinglist: true,
  investor_linkedin_url: "https://linkedin.com/in/liming",
  firm_name: "创新资本",
  firm_website_url: "https://innovationcapital.com",
  investor_type: "venture_capital",
  investment_stages: ["seed", "series_a"],
  preferred_verticals: ["ai", "healthcare", "fintech"],
  geography_preference: ["china_mainland", "greater_china"],
  average_check_size: "$250k - $1M",
  thesis_keywords: "人工智能, 医疗健康, 企业服务, 早期投资",
  source: "Investor Form",
  investor_wechat_id_cn: "liming_vc",
  investor_wechat_official_account: "创新资本",
  focus_on_chuhai_cn: true
};

// Function to submit test founder data
export const submitTestFounderData = async (): Promise<boolean> => {
  try {
    console.log('🚀 Starting test founder submission...');
    console.log('📊 Test founder data:', testFounderData);

    // Step 1: Test NocoDB submission
    console.log('📝 Step 1: Submitting to NocoDB...');
    const nocoResult = await nocoDBService.submitFounderData(testFounderData);

    if (!nocoResult.success) {
      console.error('❌ NocoDB submission failed:', nocoResult.error);
      return false;
    }

    console.log('✅ NocoDB submission successful:', nocoResult.data);

    // Step 2: Test email notifications
    console.log('📧 Step 2: Testing email notifications...');

    try {
      // Test admin notification
      console.log('📧 Sending admin notification...');
      const adminEmailResult = await emailService.sendAdminNotification({
        type: 'founder',
        data: testFounderData
      });

      if (adminEmailResult) {
        console.log('✅ Admin notification sent successfully');
      } else {
        console.warn('⚠️ Admin notification failed');
      }

      // Test confirmation email (both languages)
      console.log('📧 Sending confirmation emails...');

      // Test English confirmation
      const confirmationEnResult = await emailService.sendConfirmationEmail(
        testFounderData.founder_email,
        'founder',
        testFounderData.founder_name,
        'en'
      );

      if (confirmationEnResult) {
        console.log('✅ English confirmation email sent successfully');
      } else {
        console.warn('⚠️ English confirmation email failed');
      }

      // Test Chinese confirmation
      const confirmationZhResult = await emailService.sendConfirmationEmail(
        testFounderData.founder_email,
        'founder',
        testFounderData.founder_name,
        'zh'
      );

      if (confirmationZhResult) {
        console.log('✅ Chinese confirmation email sent successfully');
      } else {
        console.warn('⚠️ Chinese confirmation email failed');
      }

    } catch (emailError) {
      console.error('⚠️ Email notifications failed:', emailError);
      // Don't fail the whole test if emails fail
    }

    console.log('🎉 Test founder submission completed successfully!');
    console.log('📋 Final data submitted:', JSON.stringify(testFounderData, null, 2));

    return true;
  } catch (error) {
    console.error('❌ Test founder submission failed:', error);
    return false;
  }
};

// Function to submit test investor data
export const submitTestInvestorData = async (): Promise<boolean> => {
  try {
    console.log('🚀 Starting test investor submission...');
    console.log('📊 Test investor data:', testInvestorData);

    // Step 1: Test NocoDB submission
    console.log('📝 Step 1: Submitting to NocoDB...');
    const nocoResult = await nocoDBService.submitInvestorData(testInvestorData);

    if (!nocoResult.success) {
      console.error('❌ NocoDB submission failed:', nocoResult.error);
      return false;
    }

    console.log('✅ NocoDB submission successful:', nocoResult.data);

    // Step 2: Test email notifications
    console.log('📧 Step 2: Testing email notifications...');

    try {
      // Test admin notification
      console.log('📧 Sending admin notification...');
      const adminEmailResult = await emailService.sendAdminNotification({
        type: 'investor',
        data: testInvestorData
      });

      if (adminEmailResult) {
        console.log('✅ Admin notification sent successfully');
      } else {
        console.warn('⚠️ Admin notification failed');
      }

      // Test confirmation email (both languages)
      console.log('📧 Sending confirmation emails...');

      // Test English confirmation
      const confirmationEnResult = await emailService.sendConfirmationEmail(
        testInvestorData.investor_email,
        'investor',
        testInvestorData.investor_name,
        'en'
      );

      if (confirmationEnResult) {
        console.log('✅ English confirmation email sent successfully');
      } else {
        console.warn('⚠️ English confirmation email failed');
      }

      // Test Chinese confirmation
      const confirmationZhResult = await emailService.sendConfirmationEmail(
        testInvestorData.investor_email,
        'investor',
        testInvestorData.investor_name,
        'zh'
      );

      if (confirmationZhResult) {
        console.log('✅ Chinese confirmation email sent successfully');
      } else {
        console.warn('⚠️ Chinese confirmation email failed');
      }

    } catch (emailError) {
      console.error('⚠️ Email notifications failed:', emailError);
      // Don't fail the whole test if emails fail
    }

    console.log('🎉 Test investor submission completed successfully!');
    console.log('📋 Final data submitted:', JSON.stringify(testInvestorData, null, 2));

    return true;
  } catch (error) {
    console.error('❌ Test investor submission failed:', error);
    return false;
  }
};

// Function to validate founder form data
export const validateFounderData = (data: Partial<TestFounderData>): string[] => {
  const errors: string[] = [];

  if (!data.founder_name) errors.push('Founder name is required');
  if (!data.founder_email) errors.push('Founder email is required');
  if (!data.project_name) errors.push('Project name is required');
  if (!data.project_oneliner) errors.push('Project one-liner is required');
  if (!data.project_description) errors.push('Project description is required');
  if (!data.project_stage) errors.push('Project stage is required');
  if (!data.industry_verticals || data.industry_verticals.length === 0) {
    errors.push('At least one industry vertical is required');
  }
  if (!data.funding_needs) errors.push('Funding needs is required');

  return errors;
};

// Function to validate investor form data
export const validateInvestorData = (data: Partial<TestInvestorData>): string[] => {
  const errors: string[] = [];

  if (!data.investor_name) errors.push('Investor name is required');
  if (!data.investor_email) errors.push('Investor email is required');
  if (!data.investor_type) errors.push('Investor type is required');
  if (!data.investment_stages || data.investment_stages.length === 0) {
    errors.push('At least one investment stage is required');
  }
  if (!data.preferred_verticals || data.preferred_verticals.length === 0) {
    errors.push('At least one preferred vertical is required');
  }
  if (!data.geography_preference || data.geography_preference.length === 0) {
    errors.push('At least one geography preference is required');
  }
  if (!data.average_check_size) errors.push('Average check size is required');

  return errors;
};

// Comprehensive test function that tests everything
export const runComprehensiveTest = async (): Promise<{
  success: boolean;
  results: {
    founderTest: boolean;
    investorTest: boolean;
    errors: string[];
  };
}> => {
  console.group('🧪 Running Comprehensive Form Test Suite');

  const results = {
    founderTest: false,
    investorTest: false,
    errors: [] as string[]
  };

  try {
    // Test first name extraction
    console.log('👤 Testing first name extraction...');
    testFirstNameExtraction();

    // Test environment configuration
    console.log('\n🔧 Checking environment configuration...');
    const envVars = {
      'VITE_NOCODB_BASE_URL': import.meta.env.VITE_NOCODB_BASE_URL,
      'VITE_NOCODB_API_TOKEN': import.meta.env.VITE_NOCODB_API_TOKEN,
      'VITE_BREVO_API_KEY': import.meta.env.VITE_BREVO_API_KEY,
      'VITE_ADMIN_EMAIL': import.meta.env.VITE_ADMIN_EMAIL,
    };

    Object.entries(envVars).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      const displayValue = value && (key.includes('TOKEN') || key.includes('KEY')) ? '[HIDDEN]' : value || 'NOT SET';
      console.log(`${status} ${key}: ${displayValue}`);

      if (!value) {
        results.errors.push(`Missing environment variable: ${key}`);
      }
    });

    // Test founder form
    console.log('\n🧑‍💼 Testing Founder Form...');
    try {
      results.founderTest = await submitTestFounderData();
      if (results.founderTest) {
        console.log('✅ Founder form test passed');
      } else {
        console.error('❌ Founder form test failed');
        results.errors.push('Founder form submission failed');
      }
    } catch (error) {
      console.error('❌ Founder form test error:', error);
      results.errors.push(`Founder form error: ${error}`);
    }

    // Test investor form
    console.log('\n💼 Testing Investor Form...');
    try {
      results.investorTest = await submitTestInvestorData();
      if (results.investorTest) {
        console.log('✅ Investor form test passed');
      } else {
        console.error('❌ Investor form test failed');
        results.errors.push('Investor form submission failed');
      }
    } catch (error) {
      console.error('❌ Investor form test error:', error);
      results.errors.push(`Investor form error: ${error}`);
    }

    // Summary
    const success = results.founderTest && results.investorTest && results.errors.length === 0;
    console.log('\n📊 Test Summary:');
    console.log(`Founder Form: ${results.founderTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Investor Form: ${results.investorTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Overall: ${success ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (results.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    console.groupEnd();

    return { success, results };

  } catch (error) {
    console.error('❌ Comprehensive test failed:', error);
    results.errors.push(`Test suite error: ${error}`);
    console.groupEnd();

    return { success: false, results };
  }
};
