// Environment configuration checker
export const checkEnvironmentConfig = () => {
  const requiredEnvVars = {
    'VITE_NOCODB_BASE_URL': import.meta.env.VITE_NOCODB_BASE_URL,
    'VITE_NOCODB_API_TOKEN': import.meta.env.VITE_NOCODB_API_TOKEN,
    'VITE_BREVO_API_KEY': import.meta.env.VITE_BREVO_API_KEY,
    'VITE_ADMIN_EMAIL': import.meta.env.VITE_ADMIN_EMAIL,
  };

  const optionalEnvVars = {
    'NODE_ENV': import.meta.env.NODE_ENV,
    'DEV': import.meta.env.DEV,
    'PROD': import.meta.env.PROD,
  };

  console.group('🔧 Environment Configuration Check');
  
  // Check required variables
  console.log('📋 Required Environment Variables:');
  let allRequiredPresent = true;
  
  Object.entries(requiredEnvVars).forEach(([key, value]) => {
    const isPresent = value !== undefined && value !== '';
    const status = isPresent ? '✅' : '❌';
    const displayValue = isPresent 
      ? (key.includes('TOKEN') || key.includes('KEY') ? '[HIDDEN]' : value)
      : 'NOT SET';
    
    console.log(`${status} ${key}: ${displayValue}`);
    
    if (!isPresent) {
      allRequiredPresent = false;
    }
  });

  // Check optional variables
  console.log('\n📋 Environment Info:');
  Object.entries(optionalEnvVars).forEach(([key, value]) => {
    console.log(`ℹ️ ${key}: ${value}`);
  });

  // Provide guidance
  if (!allRequiredPresent) {
    console.error('\n❌ Missing required environment variables!');
    console.error('Create a .env file in your project root with:');
    console.error(`
VITE_NOCODB_BASE_URL=http://34.40.68.239:8080
VITE_NOCODB_API_TOKEN=your-actual-token
VITE_BREVO_API_KEY=your-actual-brevo-key
VITE_ADMIN_EMAIL=<EMAIL>
    `);
  } else {
    console.log('\n✅ All required environment variables are configured!');
  }

  console.groupEnd();
  
  return allRequiredPresent;
};

// Auto-run in development
if (import.meta.env.DEV) {
  checkEnvironmentConfig();
}
