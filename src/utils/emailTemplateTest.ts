// Test utility to verify email template ID mapping
// This can be used for debugging email template selection

export const getTemplateId = (userType: 'founder' | 'investor', lang: 'en' | 'zh'): number => {
  if (userType === 'founder') {
    return lang === 'zh' ? 1 : 2; // founder_zh = 1, founder_en = 2
  } else {
    return lang === 'zh' ? 5 : 4; // investor_zh = 5, investor_en = 4
  }
};

export const testTemplateMapping = () => {
  console.log('Email Template ID Mapping:');
  console.log('founder_en:', getTemplateId('founder', 'en')); // Should be 2
  console.log('founder_zh:', getTemplateId('founder', 'zh')); // Should be 1
  console.log('investor_en:', getTemplateId('investor', 'en')); // Should be 4
  console.log('investor_zh:', getTemplateId('investor', 'zh')); // Should be 5
};

// Template mapping reference:
// Based on your Brevo templates:
// #1 = founder_zh (Chinese founder confirmation)
// #2 = founder_en (English founder confirmation) 
// #4 = investor_en (English investor confirmation)
// #5 = investor_zh (Chinese investor confirmation)
