import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  submitTestFounderData,
  submitTestInvestorData,
  testFounderData,
  testInvestorData,
  validateFounderData,
  validateInvestorData,
  runComprehensiveTest
} from '@/utils/testSubmissions';
import { CheckCircle, XCircle, Play, FileText } from 'lucide-react';

interface TestResult {
  success: boolean;
  message: string;
  timestamp: Date;
}

const TestSubmissionPanel: React.FC = () => {
  const [founderResult, setFounderResult] = useState<TestResult | null>(null);
  const [investorResult, setInvestorResult] = useState<TestResult | null>(null);
  const [comprehensiveResult, setComprehensiveResult] = useState<TestResult | null>(null);
  const [isTestingFounder, setIsTestingFounder] = useState(false);
  const [isTestingInvestor, setIsTestingInvestor] = useState(false);
  const [isTestingComprehensive, setIsTestingComprehensive] = useState(false);

  const handleTestFounderSubmission = async () => {
    setIsTestingFounder(true);
    console.log('🧑‍💼 Starting individual founder form test...');
    try {
      const success = await submitTestFounderData();
      setFounderResult({
        success,
        message: success
          ? '✅ Founder form test successful! Data submitted to NocoDB and emails sent.'
          : '❌ Founder form test failed! Check console for details.',
        timestamp: new Date()
      });
    } catch (error) {
      console.error('❌ Founder form test error:', error);
      setFounderResult({
        success: false,
        message: `❌ Founder form test failed: ${error}`,
        timestamp: new Date()
      });
    } finally {
      setIsTestingFounder(false);
    }
  };

  const handleTestInvestorSubmission = async () => {
    setIsTestingInvestor(true);
    console.log('💼 Starting individual investor form test...');
    try {
      const success = await submitTestInvestorData();
      setInvestorResult({
        success,
        message: success
          ? '✅ Investor form test successful! Data submitted to NocoDB and emails sent.'
          : '❌ Investor form test failed! Check console for details.',
        timestamp: new Date()
      });
    } catch (error) {
      console.error('❌ Investor form test error:', error);
      setInvestorResult({
        success: false,
        message: `❌ Investor form test failed: ${error}`,
        timestamp: new Date()
      });
    } finally {
      setIsTestingInvestor(false);
    }
  };

  const handleComprehensiveTest = async () => {
    setIsTestingComprehensive(true);
    try {
      const result = await runComprehensiveTest();
      setComprehensiveResult({
        success: result.success,
        message: result.success
          ? 'All tests passed! ✅ NocoDB and Email services are working correctly.'
          : `Some tests failed. Errors: ${result.results.errors.join(', ')}`,
        timestamp: new Date()
      });
    } catch (error) {
      setComprehensiveResult({
        success: false,
        message: `Comprehensive test failed: ${error}`,
        timestamp: new Date()
      });
    } finally {
      setIsTestingComprehensive(false);
    }
  };

  const founderValidationErrors = validateFounderData(testFounderData);
  const investorValidationErrors = validateInvestorData(testInvestorData);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Form Test Submission Panel</h1>
        <p className="text-gray-600">Test the founder and investor form submissions with pre-filled data</p>
      </div>

      {/* Comprehensive Test Section */}
      <Card className="border-2 border-[#40826D] bg-gradient-to-r from-green-50 to-emerald-50 mb-6">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-[#40826D]">
            <Play className="w-6 h-6" />
            <span>🧪 Comprehensive Test Suite</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-700">
            <p className="mb-2">This test will:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>✅ Check environment configuration</li>
              <li>📝 Test NocoDB database submissions</li>
              <li>📧 Test email notifications (admin + confirmation)</li>
              <li>🌐 Test both English and Chinese email templates</li>
              <li>🔍 Provide detailed debugging information</li>
            </ul>
          </div>

          <Button
            onClick={handleComprehensiveTest}
            disabled={isTestingComprehensive}
            className="w-full bg-[#40826D] hover:bg-[#2D5A4A] text-white font-semibold py-3"
            size="lg"
          >
            {isTestingComprehensive ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Running Comprehensive Test...
              </>
            ) : (
              <>
                <Play className="w-5 h-5 mr-2" />
                🚀 Run Full Test Suite
              </>
            )}
          </Button>

          {comprehensiveResult && (
            <div className={`p-4 rounded-md ${comprehensiveResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <div className="flex items-center space-x-2">
                {comprehensiveResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className={`text-sm font-medium ${comprehensiveResult.success ? 'text-green-800' : 'text-red-800'}`}>
                  {comprehensiveResult.message}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {comprehensiveResult.timestamp.toLocaleString()}
              </p>
              <p className="text-xs text-gray-600 mt-1">
                Check the browser console for detailed logs
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Founder Form Test */}
        <Card className="border border-gray-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-[#40826D]" />
              <span>Founder Form Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Test Data Preview:</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>Name:</strong> {testFounderData.founder_name}</p>
                <p><strong>Email:</strong> {testFounderData.founder_email}</p>
                <p><strong>Project:</strong> {testFounderData.project_name}</p>
                <p><strong>Stage:</strong> {testFounderData.project_stage}</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {testFounderData.industry_verticals.map((vertical) => (
                    <Badge key={vertical} variant="secondary" className="text-xs">
                      {vertical}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Validation:</h4>
              {founderValidationErrors.length === 0 ? (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">All required fields valid</span>
                </div>
              ) : (
                <div className="space-y-1">
                  {founderValidationErrors.map((error, index) => (
                    <div key={index} className="flex items-center space-x-2 text-red-600">
                      <XCircle className="w-4 h-4" />
                      <span className="text-sm">{error}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <Button 
              onClick={handleTestFounderSubmission}
              disabled={isTestingFounder || founderValidationErrors.length > 0}
              className="w-full bg-[#40826D] hover:bg-[#2D5A4A]"
            >
              {isTestingFounder ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Testing...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Test Founder Submission
                </>
              )}
            </Button>

            {founderResult && (
              <div className={`p-3 rounded-md ${founderResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <div className="flex items-center space-x-2">
                  {founderResult.success ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm font-medium ${founderResult.success ? 'text-green-800' : 'text-red-800'}`}>
                    {founderResult.message}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {founderResult.timestamp.toLocaleString()}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Investor Form Test */}
        <Card className="border border-gray-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-[#40826D]" />
              <span>Investor Form Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Test Data Preview:</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>Name:</strong> {testInvestorData.investor_name}</p>
                <p><strong>Email:</strong> {testInvestorData.investor_email}</p>
                <p><strong>Firm:</strong> {testInvestorData.firm_name}</p>
                <p><strong>Type:</strong> {testInvestorData.investor_type}</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {testInvestorData.investment_stages.map((stage) => (
                    <Badge key={stage} variant="secondary" className="text-xs">
                      {stage}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Validation:</h4>
              {investorValidationErrors.length === 0 ? (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">All required fields valid</span>
                </div>
              ) : (
                <div className="space-y-1">
                  {investorValidationErrors.map((error, index) => (
                    <div key={index} className="flex items-center space-x-2 text-red-600">
                      <XCircle className="w-4 h-4" />
                      <span className="text-sm">{error}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <Button 
              onClick={handleTestInvestorSubmission}
              disabled={isTestingInvestor || investorValidationErrors.length > 0}
              className="w-full bg-[#40826D] hover:bg-[#2D5A4A]"
            >
              {isTestingInvestor ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Testing...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Test Investor Submission
                </>
              )}
            </Button>

            {investorResult && (
              <div className={`p-3 rounded-md ${investorResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <div className="flex items-center space-x-2">
                  {investorResult.success ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm font-medium ${investorResult.success ? 'text-green-800' : 'text-red-800'}`}>
                    {investorResult.message}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {investorResult.timestamp.toLocaleString()}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="text-center text-sm text-gray-500 mt-8">
        <p>Check the browser console for detailed submission logs</p>
      </div>
    </div>
  );
};

export default TestSubmissionPanel;
