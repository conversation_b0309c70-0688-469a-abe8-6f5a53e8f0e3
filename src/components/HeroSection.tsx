import React, { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import CrystalGem from './CrystalGem';

interface HeroSectionProps {
  onJoinNowClick?: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onJoinNowClick }) => {
  const { t } = useTranslation();

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Animated gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a2f2a] via-[#40826D] to-[#0d1b17] opacity-20"></div>
        
        {/* Animated particles background */}
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 20% 50%, rgba(64, 130, 109, 0.1) 0%, transparent 50%), 
                           radial-gradient(circle at 80% 20%, rgba(0, 255, 170, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 40% 80%, rgba(45, 90, 74, 0.1) 0%, transparent 50%)`
        }}></div>
        
        {/* Subtle grid pattern */}
        <div 
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `linear-gradient(rgba(64, 130, 109, 0.1) 1px, transparent 1px),
                             linear-gradient(90deg, rgba(64, 130, 109, 0.1) 1px, transparent 1px)`,
            backgroundSize: '50px 50px'
          }}
        ></div>
      </div>

      {/* Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          
          {/* Left Column - Text Content */}
          <div className="text-center lg:text-left space-y-8">
            <div className="space-y-4">
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
                <span className="block">{t('homepage.hero.title')}</span>
                <span className="block bg-gradient-to-r from-[#40826D] to-[#00ffaa] bg-clip-text text-transparent">
                  {t('homepage.hero.titleHighlight')}
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-gray-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {t('homepage.hero.subtitle')}
              </p>
            </div>

            {/* CTA Button */}
            <div className="flex justify-center lg:justify-start">
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                onClick={onJoinNowClick}
              >
                {t('homepage.hero.joinUs')}
              </Button>
            </div>

            {/* Stats or Features */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-[#40826D]">AI</div>
                <div className="text-sm text-gray-400">Powered</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-[#40826D]">Global</div>
                <div className="text-sm text-gray-400">Reach</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-[#40826D]">Smart</div>
                <div className="text-sm text-gray-400">Matching</div>
              </div>
            </div>
          </div>

          {/* Right Column - 3D Crystal Animation */}
          <div className="relative h-96 lg:h-[600px]">
            <Suspense fallback={
              <div className="w-full h-full flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#40826D]"></div>
              </div>
            }>
              <CrystalGem />
            </Suspense>
            
            {/* Glow effect behind crystal */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#40826D]/20 to-[#00ffaa]/20 blur-3xl -z-10 animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-[#40826D] rounded-full flex justify-center">
          <div className="w-1 h-3 bg-[#40826D] rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>

      {/* Floating elements for extra visual interest */}
      <div className="absolute top-20 left-10 w-4 h-4 bg-[#40826D] rounded-full opacity-20 animate-ping"></div>
      <div className="absolute top-40 right-20 w-2 h-2 bg-[#00ffaa] rounded-full opacity-30 animate-pulse"></div>
      <div className="absolute bottom-40 left-20 w-3 h-3 bg-[#40826D] rounded-full opacity-25 animate-bounce"></div>
      <div className="absolute bottom-20 right-10 w-5 h-5 bg-[#00ffaa] rounded-full opacity-15 animate-ping"></div>
    </section>
  );
};

export default HeroSection;
