
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import LanguageSwitcher from '@/components/LanguageSwitcher';

interface HeaderProps {
  onJoinNowClick?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onJoinNowClick }) => {
  const location = useLocation();
  const { t } = useTranslation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <header className="relative sticky top-0 z-50 overflow-hidden">
      {/* Metallic viridian gradient base */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1a2f2a] via-[#40826D] to-[#0d1b17]"></div>
      
      {/* Wave ripple effects */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00ffaa]/20 to-transparent animate-[wave_4s_ease-in-out_infinite]"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00d4aa]/15 to-transparent animate-[wave_4s_ease-in-out_infinite_1s]"></div>
      </div>
      
      {/* Metallic shine overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-[shimmer_6s_ease-in-out_infinite]"></div>
      
      {/* Holographic crystal patterns */}
      <div className="absolute inset-0 opacity-20" style={{
        backgroundImage: `radial-gradient(circle at 20% 50%, #00ffaa 0%, transparent 50%), 
                         radial-gradient(circle at 80% 20%, #00d4aa 0%, transparent 50%),
                         linear-gradient(45deg, transparent 40%, rgba(0,255,170,0.1) 50%, transparent 60%)`
      }}></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 flex items-center justify-center">
              <img
                src="/logo.png"
                alt="Veridian Vista Logo"
                className="w-10 h-10 object-contain drop-shadow-lg"
                onError={(e) => {
                  // Fallback to a simple text logo if image fails to load
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.parentElement!.innerHTML = '<div class="text-white font-bold text-xl">VV</div>';
                }}
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                Veridian Vista
              </h1>
            </div>
          </div>
          
          <nav className="hidden md:flex space-x-8 items-center">
            <Link
              to="/"
              className={`font-bold transition-colors ${
                isActive('/')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.home')}
            </Link>

            <Link
              to="/founder"
              className={`font-bold transition-colors ${
                isActive('/founder')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.founder')}
            </Link>

            <Link
              to="/investor"
              className={`font-bold transition-colors ${
                isActive('/investor')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.investor')}
            </Link>

            <Link
              to="/about"
              className={`font-bold transition-colors ${
                isActive('/about')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.about')}
            </Link>
          </nav>

          <div className="flex items-center space-x-3">
            <LanguageSwitcher />
            <Button
              variant="frost"
              className="px-6 py-2 whitespace-nowrap"
              onClick={() => {
                if (onJoinNowClick) {
                  onJoinNowClick();
                } else {
                  // Fallback to scrolling to footer if no handler provided
                  const contactSection = document.querySelector('footer');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }
              }}
            >
              {t('homepage.hero.joinUs')}
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
