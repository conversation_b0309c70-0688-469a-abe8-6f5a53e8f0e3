import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { gsap } from 'gsap';

// Pure Three.js Crystal Gem Component
const CrystalGem: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const crystalRef = useRef<THREE.Group | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      45,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 8);

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create crystal group
    const crystalGroup = new THREE.Group();
    crystalRef.current = crystalGroup;
    scene.add(crystalGroup);

    // Create polyhedral sphere geometry (icosahedron for crystal-like facets)
    const createPolyhedralSphere = () => {
      const geometry = new THREE.IcosahedronGeometry(2, 2); // radius 2, detail level 2
      return geometry;
    };

    // Create world map texture
    const createWorldMapTexture = () => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      canvas.width = 1024;
      canvas.height = 512;

      // Create a simple world map pattern
      context.fillStyle = '#2D5A4A'; // Dark green base
      context.fillRect(0, 0, canvas.width, canvas.height);

      // Draw continents as lighter green areas
      context.fillStyle = '#40826D';

      // North America
      context.fillRect(150, 120, 180, 120);
      context.fillRect(120, 180, 80, 60);

      // South America
      context.fillRect(200, 280, 80, 160);

      // Europe
      context.fillRect(480, 100, 60, 80);

      // Africa
      context.fillRect(460, 180, 100, 180);

      // Asia
      context.fillRect(540, 80, 200, 140);
      context.fillRect(600, 140, 150, 100);

      // Australia
      context.fillRect(720, 320, 80, 60);

      // Add some islands and details
      context.fillStyle = '#5BA085';
      for (let i = 0; i < 50; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const size = Math.random() * 10 + 2;
        context.fillRect(x, y, size, size);
      }

      const texture = new THREE.CanvasTexture(canvas);
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.needsUpdate = true;
      return texture;
    };

    // Crystal green material with world map
    const worldMapTexture = createWorldMapTexture();
    const crystalMaterial = new THREE.MeshPhysicalMaterial({
      map: worldMapTexture,
      color: new THREE.Color('#7BC4A4'), // Crystal green
      metalness: 0.1,
      roughness: 0.2,
      transmission: 0.7,
      thickness: 1.2,
      ior: 1.5,
      reflectivity: 0.9,
      clearcoat: 1.0,
      clearcoatRoughness: 0.1,
      transparent: true,
      opacity: 0.85,
      side: THREE.DoubleSide,
      envMapIntensity: 1.5
    });

    // Create main crystal sphere mesh
    const crystalGeometry = createPolyhedralSphere();
    const crystalMesh = new THREE.Mesh(crystalGeometry, crystalMaterial);
    crystalMesh.castShadow = true;
    crystalMesh.receiveShadow = true;
    crystalGroup.add(crystalMesh);

    // No text sprites - clean sphere

    // Create particle system
    const particleCount = 100;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const radius = 4 + Math.random() * 3;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      particlePositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i * 3 + 2] = radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    const particleMaterial = new THREE.PointsMaterial({
      color: '#7BC4A4', // Match the lighter sphere color
      size: 0.1,
      transparent: true,
      opacity: 0.6,
      sizeAttenuation: true
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);

    // Enhanced lighting setup for crystal shine
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    scene.add(ambientLight);

    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.2);
    directionalLight1.position.set(10, 10, 5);
    directionalLight1.castShadow = true;
    directionalLight1.shadow.mapSize.width = 2048;
    directionalLight1.shadow.mapSize.height = 2048;
    scene.add(directionalLight1);

    const directionalLight2 = new THREE.DirectionalLight(0x7BC4A4, 0.6);
    directionalLight2.position.set(-10, -10, -5);
    scene.add(directionalLight2);

    const pointLight1 = new THREE.PointLight(0x7BC4A4, 1.0, 100);
    pointLight1.position.set(5, 5, 8);
    scene.add(pointLight1);

    const pointLight2 = new THREE.PointLight(0xffffff, 0.8, 100);
    pointLight2.position.set(-5, -5, 8);
    scene.add(pointLight2);

    // Rim light for crystal edge glow
    const rimLight = new THREE.DirectionalLight(0x7BC4A4, 0.8);
    rimLight.position.set(0, 0, -10);
    scene.add(rimLight);

    // GSAP Animations
    // Crystal horizontal rotation only (like Earth spinning)
    gsap.to(crystalGroup.rotation, {
      y: Math.PI * 2,
      duration: 15,
      ease: "none",
      repeat: -1
    });

    // Crystal floating animation
    gsap.to(crystalGroup.position, {
      y: 0.3,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Subtle scale pulsing for "breathing" effect
    gsap.to(crystalGroup.scale, {
      x: 1.05,
      y: 1.05,
      z: 1.05,
      duration: 5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Particle rotation animation
    gsap.to(particles.rotation, {
      y: Math.PI * 2,
      duration: 30,
      ease: "none",
      repeat: -1
    });

    // Light intensity animations for shining effect
    gsap.to(pointLight1, {
      intensity: 1.5,
      duration: 3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(pointLight2, {
      intensity: 1.2,
      duration: 2.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1
    });

    gsap.to(rimLight, {
      intensity: 1.2,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Render loop
    const animate = () => {
      requestAnimationFrame(animate);

      // Only horizontal particle rotation
      particles.rotation.y += 0.001;

      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;

      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;

      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Dispose of geometries and materials
      crystalGeometry.dispose();
      crystalMaterial.dispose();
      worldMapTexture.dispose();
      particleGeometry.dispose();
      particleMaterial.dispose();

      // Remove renderer
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();

      // Kill GSAP animations
      gsap.killTweensOf(crystalGroup.rotation);
      gsap.killTweensOf(crystalGroup.position);
      gsap.killTweensOf(crystalGroup.scale);
      gsap.killTweensOf(particles.rotation);
      gsap.killTweensOf(pointLight1);
      gsap.killTweensOf(pointLight2);
      gsap.killTweensOf(rimLight);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-full"
      style={{ minHeight: '400px' }}
    />
  );
};

export default CrystalGem;
