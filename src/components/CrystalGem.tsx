import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { gsap } from 'gsap';

// Pure Three.js Crystal Gem Component
const CrystalGem: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const crystalRef = useRef<THREE.Group | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      45,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 8);

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create crystal group
    const crystalGroup = new THREE.Group();
    crystalRef.current = crystalGroup;
    scene.add(crystalGroup);

    // Create sphere geometry (Earth-like ball)
    const createSphereGeometry = () => {
      const geometry = new THREE.SphereGeometry(2, 64, 32);
      return geometry;
    };

    // Sphere material with lighter viridian color and Earth-like properties
    const sphereMaterial = new THREE.MeshPhysicalMaterial({
      color: new THREE.Color('#7BC4A4'), // Lighter viridian color
      metalness: 0.2,
      roughness: 0.3,
      transmission: 0.6,
      thickness: 0.8,
      ior: 1.4,
      reflectivity: 0.6,
      clearcoat: 0.8,
      clearcoatRoughness: 0.2,
      transparent: true,
      opacity: 0.9,
      side: THREE.DoubleSide
    });

    // Create main sphere mesh
    const sphereGeometry = createSphereGeometry();
    const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
    sphereMesh.castShadow = true;
    sphereMesh.receiveShadow = true;
    crystalGroup.add(sphereMesh);

    // No text sprites - clean sphere

    // Create particle system
    const particleCount = 100;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const radius = 4 + Math.random() * 3;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      particlePositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i * 3 + 2] = radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    const particleMaterial = new THREE.PointsMaterial({
      color: '#7BC4A4', // Match the lighter sphere color
      size: 0.1,
      transparent: true,
      opacity: 0.6,
      sizeAttenuation: true
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);

    // Lighting setup
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
    scene.add(ambientLight);

    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight1.position.set(10, 10, 5);
    directionalLight1.castShadow = true;
    directionalLight1.shadow.mapSize.width = 2048;
    directionalLight1.shadow.mapSize.height = 2048;
    scene.add(directionalLight1);

    const directionalLight2 = new THREE.DirectionalLight(0x40826D, 0.5);
    directionalLight2.position.set(-10, -10, -5);
    scene.add(directionalLight2);

    const pointLight = new THREE.PointLight(0x00ffaa, 0.8, 100);
    pointLight.position.set(0, 0, 10);
    scene.add(pointLight);

    // GSAP Animations
    // Crystal rotation animation
    gsap.to(crystalGroup.rotation, {
      y: Math.PI * 2,
      duration: 20,
      ease: "none",
      repeat: -1
    });

    // Crystal floating animation
    gsap.to(crystalGroup.position, {
      y: 0.5,
      duration: 3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Crystal scale pulsing
    gsap.to(crystalGroup.scale, {
      x: 1.1,
      y: 1.1,
      z: 1.1,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Particle rotation animation
    gsap.to(particles.rotation, {
      y: Math.PI * 2,
      duration: 30,
      ease: "none",
      repeat: -1
    });

    // Light intensity animation
    gsap.to(pointLight, {
      intensity: 1.5,
      duration: 2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Render loop
    const animate = () => {
      requestAnimationFrame(animate);

      // Additional subtle rotations
      crystalGroup.rotation.x += 0.002;
      particles.rotation.x += 0.001;

      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;

      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;

      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Dispose of geometries and materials
      sphereGeometry.dispose();
      sphereMaterial.dispose();
      particleGeometry.dispose();
      particleMaterial.dispose();

      // Remove renderer
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();

      // Kill GSAP animations
      gsap.killTweensOf(crystalGroup.rotation);
      gsap.killTweensOf(crystalGroup.position);
      gsap.killTweensOf(crystalGroup.scale);
      gsap.killTweensOf(particles.rotation);
      gsap.killTweensOf(pointLight);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-full"
      style={{ minHeight: '400px' }}
    />
  );
};

export default CrystalGem;
