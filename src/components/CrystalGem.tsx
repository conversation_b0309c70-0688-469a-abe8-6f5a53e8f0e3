import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text, MeshTransmissionMaterial, Environment, Float } from '@react-three/drei';
import * as THREE from 'three';

// Crystal Gem Component
const CrystalGemMesh = () => {
  const meshRef = useRef<THREE.Mesh>(null);
  const textRef = useRef<THREE.Group>(null);

  // Create crystal geometry (octahedron-like shape)
  const geometry = useMemo(() => {
    const geo = new THREE.OctahedronGeometry(2, 2);
    // Add some randomness to make it more crystal-like
    const positions = geo.attributes.position.array;
    for (let i = 0; i < positions.length; i += 3) {
      const vertex = new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]);
      vertex.normalize();
      vertex.multiplyScalar(2 + Math.random() * 0.3);
      positions[i] = vertex.x;
      positions[i + 1] = vertex.y;
      positions[i + 2] = vertex.z;
    }
    geo.attributes.position.needsUpdate = true;
    geo.computeVertexNormals();
    return geo;
  }, []);

  // Animation loop
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
      meshRef.current.rotation.x += 0.005;
      
      // Add subtle floating motion
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.2;
    }
    
    if (textRef.current) {
      textRef.current.rotation.y += 0.01;
    }
  });

  return (
    <Float speed={1.5} rotationIntensity={0.5} floatIntensity={0.5}>
      <group>
        {/* Main Crystal */}
        <mesh ref={meshRef} geometry={geometry}>
          <MeshTransmissionMaterial
            backside
            samples={16}
            resolution={512}
            transmission={0.95}
            roughness={0.1}
            thickness={1.5}
            ior={1.5}
            chromaticAberration={0.1}
            anisotropy={0.3}
            distortion={0.2}
            distortionScale={0.5}
            temporalDistortion={0.1}
            color="#40826D"
            attenuationColor="#2D5A4A"
            attenuationDistance={0.5}
          />
        </mesh>

        {/* Text on crystal surface */}
        <group ref={textRef}>
          {/* Front face text */}
          <Text
            position={[0, 0, 2.2]}
            fontSize={0.3}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#40826D"
          >
            VERIDIAN
          </Text>
          
          {/* Back face text */}
          <Text
            position={[0, 0, -2.2]}
            rotation={[0, Math.PI, 0]}
            fontSize={0.3}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#40826D"
          >
            VISTA
          </Text>
          
          {/* Side texts */}
          <Text
            position={[2.2, 0, 0]}
            rotation={[0, Math.PI / 2, 0]}
            fontSize={0.25}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#40826D"
          >
            AI
          </Text>
          
          <Text
            position={[-2.2, 0, 0]}
            rotation={[0, -Math.PI / 2, 0]}
            fontSize={0.25}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#40826D"
          >
            INVEST
          </Text>
          
          {/* Top and bottom texts */}
          <Text
            position={[0, 2.2, 0]}
            rotation={[-Math.PI / 2, 0, 0]}
            fontSize={0.2}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#40826D"
          >
            SMART
          </Text>
          
          <Text
            position={[0, -2.2, 0]}
            rotation={[Math.PI / 2, 0, 0]}
            fontSize={0.2}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#40826D"
          >
            FUTURE
          </Text>
        </group>

        {/* Particle effects around the crystal */}
        <ParticleField />
      </group>
    </Float>
  );
};

// Particle field for magical effect
const ParticleField = () => {
  const particlesRef = useRef<THREE.Points>(null);
  
  const particles = useMemo(() => {
    const count = 100;
    const positions = new Float32Array(count * 3);
    
    for (let i = 0; i < count; i++) {
      const radius = 4 + Math.random() * 3;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      
      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[i * 3 + 2] = radius * Math.cos(phi);
    }
    
    return positions;
  }, []);

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y += 0.002;
      particlesRef.current.rotation.x += 0.001;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particles.length / 3}
          array={particles}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.05}
        color="#40826D"
        transparent
        opacity={0.6}
        sizeAttenuation
      />
    </points>
  );
};

// Main Crystal Gem Component
const CrystalGem: React.FC = () => {
  return (
    <div className="w-full h-full">
      <Canvas
        camera={{ position: [0, 0, 8], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.3} />
        <directionalLight position={[10, 10, 5]} intensity={1} color="#ffffff" />
        <directionalLight position={[-10, -10, -5]} intensity={0.5} color="#40826D" />
        <pointLight position={[0, 0, 10]} intensity={0.8} color="#00ffaa" />
        
        {/* Environment for reflections */}
        <Environment preset="city" />
        
        {/* Crystal Gem */}
        <CrystalGemMesh />
        
        {/* Fog for depth */}
        <fog attach="fog" args={['#000000', 10, 20]} />
      </Canvas>
    </div>
  );
};

export default CrystalGem;
