import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { gsap } from 'gsap';

// Pure Three.js Crystal Gem Component
const CrystalGem: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const crystalRef = useRef<THREE.Group | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      45,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 8);

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create crystal group
    const crystalGroup = new THREE.Group();
    crystalRef.current = crystalGroup;
    scene.add(crystalGroup);

    // Create abstract diamond geometry with complex facets
    const createAbstractDiamond = () => {
      const geometry = new THREE.OctahedronGeometry(2.5, 3); // More detailed facets

      // Add complexity by modifying vertices for more abstract shape
      const positions = geometry.attributes.position.array as Float32Array;
      for (let i = 0; i < positions.length; i += 3) {
        const vertex = new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]);
        vertex.normalize();

        // Create complex surface variations
        const noise = Math.sin(vertex.x * 4) * Math.cos(vertex.y * 3) * Math.sin(vertex.z * 5) * 0.15;
        const scale = 2.5 + noise;
        vertex.multiplyScalar(scale);

        positions[i] = vertex.x;
        positions[i + 1] = vertex.y;
        positions[i + 2] = vertex.z;
      }

      geometry.attributes.position.needsUpdate = true;
      geometry.computeVertexNormals();
      return geometry;
    };

    // Create flowing data code texture
    const createDataCodeTexture = () => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      canvas.width = 1024;
      canvas.height = 1024;

      // Dark transparent base
      context.fillStyle = 'rgba(0, 0, 0, 0.1)';
      context.fillRect(0, 0, canvas.width, canvas.height);

      // Create flowing data streams
      const codeChars = '01ABCDEFabcdef{}[]()<>+-*/=';
      context.font = '12px monospace';

      // Multiple data streams
      for (let stream = 0; stream < 20; stream++) {
        const x = (stream * 50) % canvas.width;
        const hue = 120 + Math.random() * 60; // Green spectrum

        for (let i = 0; i < 80; i++) {
          const y = (i * 15) % canvas.height;
          const char = codeChars[Math.floor(Math.random() * codeChars.length)];
          const alpha = Math.random() * 0.8 + 0.2;
          const brightness = Math.random() * 60 + 40;

          context.fillStyle = `hsla(${hue}, 80%, ${brightness}%, ${alpha})`;
          context.fillText(char, x, y);
        }
      }

      // Add glowing particles
      for (let i = 0; i < 100; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const size = Math.random() * 3 + 1;
        const hue = 120 + Math.random() * 60;

        context.beginPath();
        context.arc(x, y, size, 0, Math.PI * 2);
        context.fillStyle = `hsla(${hue}, 100%, 70%, 0.6)`;
        context.fill();
      }

      const texture = new THREE.CanvasTexture(canvas);
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.needsUpdate = true;
      return texture;
    };

    // Create emerald gradient material with glass effect
    const dataTexture = createDataCodeTexture();
    const diamondMaterial = new THREE.MeshPhysicalMaterial({
      map: dataTexture,
      color: new THREE.Color('#00ff88'), // Bright emerald
      metalness: 0.05,
      roughness: 0.05,
      transmission: 0.95,
      thickness: 2.0,
      ior: 2.4, // Diamond-like refraction
      reflectivity: 1.0,
      clearcoat: 1.0,
      clearcoatRoughness: 0.0,
      transparent: true,
      opacity: 0.9,
      side: THREE.DoubleSide,
      envMapIntensity: 2.0,
      sheen: 1.0,
      sheenColor: new THREE.Color('#88ffaa')
    });

    // Create main abstract diamond mesh
    const diamondGeometry = createAbstractDiamond();
    const diamondMesh = new THREE.Mesh(diamondGeometry, diamondMaterial);
    diamondMesh.castShadow = true;
    diamondMesh.receiveShadow = true;
    crystalGroup.add(diamondMesh);

    // Create inner glow core
    const coreGeometry = new THREE.SphereGeometry(1.5, 32, 32);
    const coreMaterial = new THREE.MeshBasicMaterial({
      color: new THREE.Color('#44ff99'),
      transparent: true,
      opacity: 0.3,
      side: THREE.BackSide
    });
    const coreMesh = new THREE.Mesh(coreGeometry, coreMaterial);
    crystalGroup.add(coreMesh);

    // Create outer energy field
    const fieldGeometry = new THREE.SphereGeometry(3.2, 16, 16);
    const fieldMaterial = new THREE.MeshBasicMaterial({
      color: new THREE.Color('#22ff77'),
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide,
      wireframe: true
    });
    const fieldMesh = new THREE.Mesh(fieldGeometry, fieldMaterial);
    crystalGroup.add(fieldMesh);

    // Create complex particle systems for tech effect

    // Data particles flowing around
    const dataParticleCount = 200;
    const dataParticleGeometry = new THREE.BufferGeometry();
    const dataPositions = new Float32Array(dataParticleCount * 3);
    const dataColors = new Float32Array(dataParticleCount * 3);
    const dataSizes = new Float32Array(dataParticleCount);

    for (let i = 0; i < dataParticleCount; i++) {
      const radius = 3.5 + Math.random() * 2;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      dataPositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      dataPositions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      dataPositions[i * 3 + 2] = radius * Math.cos(phi);

      // Green spectrum colors
      const hue = 0.3 + Math.random() * 0.2; // Green range
      const color = new THREE.Color().setHSL(hue, 0.8, 0.6);
      dataColors[i * 3] = color.r;
      dataColors[i * 3 + 1] = color.g;
      dataColors[i * 3 + 2] = color.b;

      dataSizes[i] = Math.random() * 0.15 + 0.05;
    }

    dataParticleGeometry.setAttribute('position', new THREE.BufferAttribute(dataPositions, 3));
    dataParticleGeometry.setAttribute('color', new THREE.BufferAttribute(dataColors, 3));
    dataParticleGeometry.setAttribute('size', new THREE.BufferAttribute(dataSizes, 1));

    const dataParticleMaterial = new THREE.PointsMaterial({
      size: 0.1,
      transparent: true,
      opacity: 0.8,
      sizeAttenuation: true,
      vertexColors: true,
      blending: THREE.AdditiveBlending
    });

    const dataParticles = new THREE.Points(dataParticleGeometry, dataParticleMaterial);
    scene.add(dataParticles);

    // Light rays/beams
    const rayCount = 12;
    const rayGroup = new THREE.Group();

    for (let i = 0; i < rayCount; i++) {
      const rayGeometry = new THREE.CylinderGeometry(0.02, 0.02, 8, 8);
      const rayMaterial = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.35, 0.8, 0.7),
        transparent: true,
        opacity: 0.4,
        blending: THREE.AdditiveBlending
      });

      const ray = new THREE.Mesh(rayGeometry, rayMaterial);
      const angle = (i / rayCount) * Math.PI * 2;
      ray.position.set(Math.cos(angle) * 4, 0, Math.sin(angle) * 4);
      ray.lookAt(0, 0, 0);
      ray.rotateX(Math.PI / 2);

      rayGroup.add(ray);
    }

    scene.add(rayGroup);

    // E-commerce grade complex lighting system
    const ambientLight = new THREE.AmbientLight(0x88ffaa, 0.3);
    scene.add(ambientLight);

    // Main key light (bright white)
    const keyLight = new THREE.DirectionalLight(0xffffff, 2.0);
    keyLight.position.set(8, 12, 6);
    keyLight.castShadow = true;
    keyLight.shadow.mapSize.width = 4096;
    keyLight.shadow.mapSize.height = 4096;
    keyLight.shadow.camera.near = 0.1;
    keyLight.shadow.camera.far = 50;
    scene.add(keyLight);

    // Fill light (emerald)
    const fillLight = new THREE.DirectionalLight(0x44ff88, 1.5);
    fillLight.position.set(-8, 8, -6);
    scene.add(fillLight);

    // Rim lights for edge definition
    const rimLight1 = new THREE.DirectionalLight(0x88ffcc, 1.2);
    rimLight1.position.set(0, 0, -12);
    scene.add(rimLight1);

    const rimLight2 = new THREE.DirectionalLight(0xaaffdd, 0.8);
    rimLight2.position.set(0, -12, 0);
    scene.add(rimLight2);

    // Multiple point lights for sparkle
    const sparkleLight1 = new THREE.PointLight(0x00ff99, 2.0, 20);
    sparkleLight1.position.set(6, 6, 8);
    scene.add(sparkleLight1);

    const sparkleLight2 = new THREE.PointLight(0x44ffaa, 1.8, 20);
    sparkleLight2.position.set(-6, -6, 8);
    scene.add(sparkleLight2);

    const sparkleLight3 = new THREE.PointLight(0x66ffbb, 1.5, 20);
    sparkleLight3.position.set(0, 8, -6);
    scene.add(sparkleLight3);

    const sparkleLight4 = new THREE.PointLight(0x88ffcc, 1.3, 20);
    sparkleLight4.position.set(8, -4, 4);
    scene.add(sparkleLight4);

    // Accent lights for depth
    const accentLight1 = new THREE.SpotLight(0x22ff77, 1.0, 30, Math.PI / 6, 0.5);
    accentLight1.position.set(10, 10, 10);
    accentLight1.target.position.set(0, 0, 0);
    scene.add(accentLight1);
    scene.add(accentLight1.target);

    const accentLight2 = new THREE.SpotLight(0x44ff99, 0.8, 30, Math.PI / 8, 0.3);
    accentLight2.position.set(-10, -10, 10);
    accentLight2.target.position.set(0, 0, 0);
    scene.add(accentLight2);
    scene.add(accentLight2.target);

    // GSAP Animations - Futuristic floating diamond

    // Main diamond rotation (slow and elegant)
    gsap.to(crystalGroup.rotation, {
      y: Math.PI * 2,
      duration: 20,
      ease: "none",
      repeat: -1
    });

    // Complex floating motion (figure-8 pattern)
    gsap.to(crystalGroup.position, {
      y: 0.5,
      duration: 6,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(crystalGroup.position, {
      x: 0.3,
      duration: 8,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1
    });

    // Breathing scale animation
    gsap.to(crystalGroup.scale, {
      x: 1.08,
      y: 1.08,
      z: 1.08,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Inner core pulsing
    gsap.to(coreMesh.scale, {
      x: 1.2,
      y: 1.2,
      z: 1.2,
      duration: 2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Energy field rotation
    gsap.to(fieldMesh.rotation, {
      x: Math.PI * 2,
      y: Math.PI * 2,
      duration: 25,
      ease: "none",
      repeat: -1
    });

    // Data particles orbital motion
    gsap.to(dataParticles.rotation, {
      y: Math.PI * 2,
      duration: 30,
      ease: "none",
      repeat: -1
    });

    // Light rays rotation
    gsap.to(rayGroup.rotation, {
      z: Math.PI * 2,
      duration: 12,
      ease: "none",
      repeat: -1
    });

    // Particle rotation animation
    gsap.to(particles.rotation, {
      y: Math.PI * 2,
      duration: 30,
      ease: "none",
      repeat: -1
    });

    // Complex light intensity animations for e-commerce sparkle
    gsap.to(sparkleLight1, {
      intensity: 3.0,
      duration: 2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(sparkleLight2, {
      intensity: 2.5,
      duration: 2.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 0.5
    });

    gsap.to(sparkleLight3, {
      intensity: 2.2,
      duration: 3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1
    });

    gsap.to(sparkleLight4, {
      intensity: 2.0,
      duration: 2.8,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1.5
    });

    // Rim light intensity variations
    gsap.to(rimLight1, {
      intensity: 1.8,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(rimLight2, {
      intensity: 1.4,
      duration: 3.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 2
    });

    // Accent light movements
    gsap.to(accentLight1.position, {
      x: 12,
      z: 12,
      duration: 8,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(accentLight2.position, {
      x: -12,
      z: 12,
      duration: 10,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1,
      delay: 4
    });

    // Render loop with data flow effects
    let time = 0;
    const animate = () => {
      requestAnimationFrame(animate);
      time += 0.016; // ~60fps

      // Update data texture for flowing code effect
      if (time % 0.1 < 0.016) { // Update every 6 frames
        const newDataTexture = createDataCodeTexture();
        diamondMaterial.map = newDataTexture;
        diamondMaterial.needsUpdate = true;
      }

      // Particle system micro-movements
      const positions = dataParticleGeometry.attributes.position.array as Float32Array;
      for (let i = 0; i < positions.length; i += 3) {
        positions[i + 1] += Math.sin(time * 2 + i) * 0.002; // Subtle vertical flow
      }
      dataParticleGeometry.attributes.position.needsUpdate = true;

      // Core material opacity pulsing
      coreMaterial.opacity = 0.2 + Math.sin(time * 3) * 0.1;

      // Field wireframe opacity
      fieldMaterial.opacity = 0.05 + Math.sin(time * 2) * 0.05;

      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;

      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;

      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Dispose of geometries and materials
      diamondGeometry.dispose();
      diamondMaterial.dispose();
      dataTexture.dispose();
      coreGeometry.dispose();
      coreMaterial.dispose();
      fieldGeometry.dispose();
      fieldMaterial.dispose();
      dataParticleGeometry.dispose();
      dataParticleMaterial.dispose();

      // Remove renderer
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();

      // Kill all GSAP animations
      gsap.killTweensOf(crystalGroup.rotation);
      gsap.killTweensOf(crystalGroup.position);
      gsap.killTweensOf(crystalGroup.scale);
      gsap.killTweensOf(coreMesh.scale);
      gsap.killTweensOf(fieldMesh.rotation);
      gsap.killTweensOf(dataParticles.rotation);
      gsap.killTweensOf(rayGroup.rotation);
      gsap.killTweensOf(sparkleLight1);
      gsap.killTweensOf(sparkleLight2);
      gsap.killTweensOf(sparkleLight3);
      gsap.killTweensOf(sparkleLight4);
      gsap.killTweensOf(rimLight1);
      gsap.killTweensOf(rimLight2);
      gsap.killTweensOf(accentLight1.position);
      gsap.killTweensOf(accentLight2.position);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-full"
      style={{ minHeight: '400px' }}
    />
  );
};

export default CrystalGem;
