import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { gsap } from 'gsap';

// Pure Three.js Crystal Gem Component
const CrystalGem: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const crystalRef = useRef<THREE.Group | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      45,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 8);

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create crystal group
    const crystalGroup = new THREE.Group();
    crystalRef.current = crystalGroup;
    scene.add(crystalGroup);

    // Create crystal geometry (diamond-like shape)
    const createCrystalGeometry = () => {
      const geometry = new THREE.OctahedronGeometry(2, 2);

      // Modify vertices to create more crystal-like shape
      const positions = geometry.attributes.position.array as Float32Array;
      for (let i = 0; i < positions.length; i += 3) {
        const vertex = new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]);
        vertex.normalize();
        // Add some randomness for more organic crystal shape
        const scale = 2 + Math.sin(vertex.x * 3) * 0.2 + Math.cos(vertex.y * 4) * 0.15;
        vertex.multiplyScalar(scale);
        positions[i] = vertex.x;
        positions[i + 1] = vertex.y;
        positions[i + 2] = vertex.z;
      }

      geometry.attributes.position.needsUpdate = true;
      geometry.computeVertexNormals();
      return geometry;
    };

    // Crystal material with glass-like properties
    const crystalMaterial = new THREE.MeshPhysicalMaterial({
      color: new THREE.Color('#40826D'),
      metalness: 0.1,
      roughness: 0.05,
      transmission: 0.9,
      thickness: 1.5,
      ior: 1.5,
      reflectivity: 0.8,
      clearcoat: 1.0,
      clearcoatRoughness: 0.1,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });

    // Create main crystal mesh
    const crystalGeometry = createCrystalGeometry();
    const crystalMesh = new THREE.Mesh(crystalGeometry, crystalMaterial);
    crystalMesh.castShadow = true;
    crystalMesh.receiveShadow = true;
    crystalGroup.add(crystalMesh);

    // Create text sprites on crystal faces
    const createTextSprite = (text: string, position: THREE.Vector3, scale = 1) => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      canvas.width = 512;
      canvas.height = 256;

      // Text styling
      context.fillStyle = 'rgba(0, 0, 0, 0)';
      context.fillRect(0, 0, canvas.width, canvas.height);

      context.font = `bold ${60 * scale}px Arial, sans-serif`;
      context.fillStyle = '#ffffff';
      context.strokeStyle = '#40826D';
      context.lineWidth = 4;
      context.textAlign = 'center';
      context.textBaseline = 'middle';

      // Draw text with outline
      context.strokeText(text, canvas.width / 2, canvas.height / 2);
      context.fillText(text, canvas.width / 2, canvas.height / 2);

      const texture = new THREE.CanvasTexture(canvas);
      texture.needsUpdate = true;

      const spriteMaterial = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        alphaTest: 0.1
      });

      const sprite = new THREE.Sprite(spriteMaterial);
      sprite.position.copy(position);
      sprite.scale.set(2 * scale, 1 * scale, 1);

      return sprite;
    };

    // Add text sprites to crystal faces
    const texts = [
      { text: 'VERIDIAN', position: new THREE.Vector3(0, 0, 2.5), scale: 1 },
      { text: 'VISTA', position: new THREE.Vector3(0, 0, -2.5), scale: 1 },
      { text: 'AI', position: new THREE.Vector3(2.5, 0, 0), scale: 0.8 },
      { text: 'INVEST', position: new THREE.Vector3(-2.5, 0, 0), scale: 0.7 },
      { text: 'SMART', position: new THREE.Vector3(0, 2.5, 0), scale: 0.6 },
      { text: 'FUTURE', position: new THREE.Vector3(0, -2.5, 0), scale: 0.6 }
    ];

    texts.forEach(({ text, position, scale }) => {
      const sprite = createTextSprite(text, position, scale);
      crystalGroup.add(sprite);
    });

    // Create particle system
    const particleCount = 100;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const radius = 4 + Math.random() * 3;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      particlePositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i * 3 + 2] = radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    const particleMaterial = new THREE.PointsMaterial({
      color: '#40826D',
      size: 0.1,
      transparent: true,
      opacity: 0.6,
      sizeAttenuation: true
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);

    // Lighting setup
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
    scene.add(ambientLight);

    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight1.position.set(10, 10, 5);
    directionalLight1.castShadow = true;
    directionalLight1.shadow.mapSize.width = 2048;
    directionalLight1.shadow.mapSize.height = 2048;
    scene.add(directionalLight1);

    const directionalLight2 = new THREE.DirectionalLight(0x40826D, 0.5);
    directionalLight2.position.set(-10, -10, -5);
    scene.add(directionalLight2);

    const pointLight = new THREE.PointLight(0x00ffaa, 0.8, 100);
    pointLight.position.set(0, 0, 10);
    scene.add(pointLight);

    // GSAP Animations
    // Crystal rotation animation
    gsap.to(crystalGroup.rotation, {
      y: Math.PI * 2,
      duration: 20,
      ease: "none",
      repeat: -1
    });

    // Crystal floating animation
    gsap.to(crystalGroup.position, {
      y: 0.5,
      duration: 3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Crystal scale pulsing
    gsap.to(crystalGroup.scale, {
      x: 1.1,
      y: 1.1,
      z: 1.1,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Particle rotation animation
    gsap.to(particles.rotation, {
      y: Math.PI * 2,
      duration: 30,
      ease: "none",
      repeat: -1
    });

    // Light intensity animation
    gsap.to(pointLight, {
      intensity: 1.5,
      duration: 2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Render loop
    const animate = () => {
      requestAnimationFrame(animate);

      // Additional subtle rotations
      crystalGroup.rotation.x += 0.002;
      particles.rotation.x += 0.001;

      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;

      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;

      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Dispose of geometries and materials
      crystalGeometry.dispose();
      crystalMaterial.dispose();
      particleGeometry.dispose();
      particleMaterial.dispose();

      // Remove renderer
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();

      // Kill GSAP animations
      gsap.killTweensOf(crystalGroup.rotation);
      gsap.killTweensOf(crystalGroup.position);
      gsap.killTweensOf(crystalGroup.scale);
      gsap.killTweensOf(particles.rotation);
      gsap.killTweensOf(pointLight);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-full"
      style={{ minHeight: '400px' }}
    />
  );
};

export default CrystalGem;
