export default {
  "header": {
    "home": "Home",
    "founder": "Founder",
    "investor": "Investor",
    "about": "About Us"
  },
  "founderForm": {
    "title": "Join Our Founder Network",
    "subtitle": "Connect with investors and fellow entrepreneurs. Share your vision and get discovered by the right partners who can help you build the future.",
    "headerTitle": "Founder Profile",
    "headerSubtitle": "Tell us about yourself and your project",
    "personalInfo": "Personal Information",
    "projectInfo": "Project Information",
    "additionalInfo": "Additional Information",
    "name": "Founder Name",
    "email": "Email Address",
    "inMailinglist": "Join our mailing list for updates",
    "linkedin": "LinkedIn Profile",
    "github": "GitHub Profile",
    "twitter": "Twitter Profile",
    "location": "Location",
    "projectName": "Project Name",
    "projectWebsite": "Project Website",
    "projectOneliner": "Project One-liner",
    "projectDescription": "Project Description",
    "projectStage": "Project Stage",
    "industryVerticals": "Industry Verticals",
    "currentTraction": "Current Traction Data",
    "fundingNeeds": "Funding Needs",
    "wechatId": "WeChat ID",
    "wechatIdNote": "Only used for us to contact you, will not be made public",
    "inWechatGroup": "Join our WeChat group for updates",
    "projectWechatAccount": "Project WeChat Official Account",
    "projectShowcaseUrl": "Project Showcase URL (China)",
    "developerCommunityUrl": "Developer Community URL (China)"
  },
  "projectStage": {
    "idea": "Idea Stage",
    "mvp": "MVP",
    "early_traction": "Early Traction",
    "seed": "Seed Stage",
    "series_a": "Series A",
    "series_b": "Series B",
    "growth": "Growth Stage"
  },
  "industry": {
    "ai": "AI/ML",
    "saas": "SaaS",
    "fintech": "Fintech",
    "biotech": "Biotech",
    "dev_tool": "Developer Tools",
    "social_media": "Social Media",
    "e_commerce": "E-commerce",
    "edutech": "EdTech",
    "enterprise": "Enterprise",
    "robotics": "Robotics",
    "hardware": "Hardware",
    "healthcare": "Healthcare",
    "proptech": "PropTech",
    "cleantech": "CleanTech",
    "mobility": "Mobility",
    "consumer": "Consumer",
    "blockchain": "Blockchain",
    "cybersecurity": "Cybersecurity",
    "entertainment": "Entertainment"
  },
  "funding": {
    "raising_funds": "Currently raising funds",
    "plan_to_raise": "Plan to raise in the future",
    "no_plan": "No fundraising plans"
  },
  "form": {
    "required": "*",
    "optional": "",
    "requiredNote": "Fields marked with a red asterisk (*) are required.",
    "requiredFieldsNote": "Required fields",
    "submit": "Submit Application",
    "submitting": "Submitting...",
    "selectIndustry": "Select industry verticals",
    "selectStage": "Select project stage",
    "selectFunding": "Select funding status",
    "selectYesNo": "Please select",
    "yes": "Yes",
    "no": "No",
    "agreementText": "By submitting this form, you agree to our",
    "and": "and"
  },
  "placeholder": {
    "name": "Your full name",
    "email": "<EMAIL>",
    "linkedin": "https://linkedin.com/in/yourprofile",
    "github": "https://github.com/yourusername",
    "twitter": "https://twitter.com/yourusername",
    "location": "City, Country",
    "projectName": "Your project name",
    "projectWebsite": "https://yourproject.com",
    "projectOneliner": "Describe your project in one sentence",
    "projectDescription": "Provide a detailed description of your project, including the problem you're solving, your solution, target market, and any unique advantages you have.",
    "currentTraction": "Share key metrics, user numbers, revenue, partnerships, or other indicators of progress",
    "website": "https://yourproject.com",
    "oneliner": "Describe your project in one sentence",
    "description": "Provide a detailed description of your project, including the problem you're solving, your solution, target market, and any unique advantages you have.",
    "traction": "Share key metrics, user numbers, revenue, partnerships, or other indicators of progress",
    "wechatId": "Your WeChat ID",
    "projectWechatAccount": "WeChat Official Account name",
    "projectShowcaseUrl": "https://yourproject.cn",
    "developerCommunityUrl": "https://community.yourproject.cn"
  },
  "message": {
    "success": "Application submitted successfully! We'll be in touch soon.",
    "error": "An error occurred. Please try again.",
    "missingRequiredFields": "Please fill in all required fields.",
    "selectAtLeastOneIndustry": "Please select at least one industry vertical."
  },
  "homepage": {
    "hero": {
      "title": "Be the smartest",
      "titleHighlight": "in the room.",
      "subtitle": "AI-powered investment intelligence that helps you discover breakthrough companies before they become obvious to everyone else.",
      "joinUs": "Join Now",
      "getStarted": "Get Started",
      "startDiscovering": "Start Discovering",
      "watchDemo": "Watch Demo"
    },
    "features": {
      "title": "AI-Powered Investment Intelligence",
      "subtitle": "Advanced technology that transforms how you discover and evaluate early-stage opportunities",
      "aiIntelligence": {
        "title": "Know who to back",
        "description": "Advanced algorithms analyze thousands of data points to identify promising opportunities"
      },
      "globalReach": {
        "title": "Global reach",
        "description": "Access investment opportunities worldwide, from emerging markets to established ecosystems"
      },
      "smartMatching": {
        "title": "Make the right decision faster",
        "description": "Intelligent matching connects you with companies that align with your investment thesis"
      },
      "dataInsights": {
        "title": "Maximize the value of your investments",
        "description": "Deep market insights and predictive analytics guide your investment decisions"
      },
      "realTimeData": {
        "title": "Maximize the value of your investments",
        "description": "Deep market insights and predictive analytics guide your investment decisions"
      },
      "aiFounderAnalysis": {
        "title": "AI Founder Analysis",
        "description": "Smart evaluation of entrepreneur backgrounds, track records, and potential using advanced machine learning algorithms"
      },
      "marketIntelligence": {
        "title": "Market Intelligence",
        "description": "Deep insights into emerging sectors, market trends, and competitive landscapes before they become mainstream"
      },
      "riskAssessment": {
        "title": "Risk Assessment",
        "description": "Predictive analytics and risk modeling to support confident investment decision-making at the earliest stages"
      }
    },
    "cta": {
      "title": "Ready to discover the next big opportunity?",
      "subtitle": "Join thousands of investors who trust Veridian Vista to identify breakthrough companies.",
      "getStarted": "Get Started Today",
      "learnMore": "Learn More"
    }
  },
  "navigation": {
    "home": "Home",
    "about": "About",
    "contact": "Contact"
  },
  "footer": {
    "company": "Company",
    "product": "Product",
    "resources": "Resources",
    "contact": "Contact",
    "description": "AI-Powered Early Investment Discovery Platform - connecting sophisticated investors with tomorrow's breakthrough companies.",
    "allRightsReserved": "All rights reserved.",
    "privacyPolicy": "Privacy Policy",
    "termsOfService": "Terms of Service",
    "forInvestors": "For Investors",
    "forFounders": "For Founders",
    "aboutUs": "About Us"
  },
  "privacyPolicy": {
    "title": "Privacy Policy",
    "lastUpdated": "Last updated",
    "date": "January 2025",
    "introduction": {
      "title": "Introduction",
      "content": "Veridian Vista is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our AI-powered investment intelligence platform."
    },
    "dataCollection": {
      "title": "Information We Collect",
      "content": "We collect information you provide directly to us, such as when you create an account, fill out forms, or contact us. This may include:",
      "items": {
        "personal": "Personal information (name, email address, contact details)",
        "professional": "Professional information (LinkedIn profile, company details)",
        "project": "Project information (business descriptions, funding needs)",
        "usage": "Usage data and analytics to improve our services"
      }
    },
    "dataUse": {
      "title": "How We Use Your Information",
      "content": "We use the information we collect to:",
      "items": {
        "matching": "Facilitate connections between investors and founders",
        "communication": "Communicate with you about our services",
        "improvement": "Improve and personalize our platform",
        "legal": "Comply with legal obligations"
      }
    },
    "dataSharing": {
      "title": "Information Sharing",
      "content": "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy or as required by law."
    },
    "dataSecurity": {
      "title": "Data Security",
      "content": "We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction."
    },
    "userRights": {
      "title": "Your Rights",
      "content": "You have the right to:",
      "items": {
        "access": "Access and receive a copy of your personal data",
        "correction": "Correct inaccurate or incomplete data",
        "deletion": "Request deletion of your personal data",
        "portability": "Data portability in certain circumstances"
      }
    },
    "contact": {
      "title": "Contact Us",
      "content": "If you have any questions about this Privacy Policy, please contact us at:",
      "email": "Email"
    }
  },
  "termsOfService": {
    "title": "Terms of Service",
    "lastUpdated": "Last updated",
    "date": "January 2025",
    "acceptance": {
      "title": "Acceptance of Terms",
      "content": "By accessing and using Veridian Vista's platform, you accept and agree to be bound by the terms and provision of this agreement."
    },
    "services": {
      "title": "Description of Service",
      "content": "Veridian Vista provides an AI-powered investment intelligence platform that connects investors with founders and startup opportunities."
    },
    "userObligations": {
      "title": "User Obligations",
      "content": "As a user of our platform, you agree to:",
      "items": {
        "accurate": "Provide accurate and complete information",
        "lawful": "Use the service only for lawful purposes",
        "respectful": "Respect the rights and privacy of other users",
        "confidential": "Maintain confidentiality of sensitive information"
      }
    },
    "intellectualProperty": {
      "title": "Intellectual Property",
      "content": "The platform and its original content, features, and functionality are owned by Veridian Vista and are protected by international copyright, trademark, and other intellectual property laws."
    },
    "limitation": {
      "title": "Limitation of Liability",
      "content": "Veridian Vista shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the platform."
    },
    "termination": {
      "title": "Termination",
      "content": "We may terminate or suspend your account and access to the service immediately, without prior notice, for conduct that we believe violates these Terms of Service."
    },
    "changes": {
      "title": "Changes to Terms",
      "content": "We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through the platform."
    },
    "contact": {
      "title": "Contact Information",
      "content": "If you have any questions about these Terms of Service, please contact us at:",
      "email": "Email"
    }
  },
  "blog": {
    "title": "Blog",
    "subtitle": "Insights, trends, and stories from the world of early-stage investing",
    "comingSoon": "Coming Soon",
    "comingSoonDescription": "We're working on bringing you the latest insights and stories from the investment world. Stay tuned!",
    "backToHome": "Back to Home"
  },
  "investorForm": {
    "title": "Investor Registration",
    "subtitle": "Join our network of investors and discover breakthrough companies",
    "pageTitle": "Join Our Investor Network",
    "pageSubtitle": "Connect with innovative startups. Complete your profile to access our curated deal flow and discover the next generation of breakthrough companies.",
    "personalInfo": "Personal Information",
    "firmInfo": "Firm Information",
    "investmentPreferences": "Investment Preferences",
    "additionalInfo": "Additional Information",
    "fields": {
      "investorName": "Full Name",
      "investorEmail": "Email Address",
      "inMailinglist": "Join our mailing list for updates",
      "investorLinkedin": "LinkedIn Profile",
      "firmName": "Firm Name",
      "firmWebsite": "Firm Website",
      "investorType": "Investor Type",
      "investmentStages": "Investment Stages",
      "preferredVerticals": "Preferred Verticals",
      "geographyPreference": "Geography Preference",
      "averageCheckSize": "Average Check Size",
      "thesisKeywords": "Investment Thesis Keywords",
      "investorWechatId": "WeChat ID",
      "investorWechatOfficialAccount": "WeChat Official Account",
      "focusOnChuhai": "Focus on Chuhai (出海) Companies"
    },
    "placeholders": {
      "investorName": "Your full name",
      "investorEmail": "<EMAIL>",
      "investorLinkedin": "https://linkedin.com/in/yourprofile",
      "firmName": "Your firm or organization name",
      "firmWebsite": "https://yourfirm.com",
      "thesisKeywords": "Describe your investment thesis and focus areas"
    },
    "investorTypes": {
      "angel_investor": "Angel Investor",
      "venture_capital": "Venture Capital",
      "family_office": "Family Office",
      "accelerator": "Accelerator",
      "incubator": "Incubator",
      "government_fund": "Government Fund",
      "private_equity": "Private Equity",
      "corporate_vc": "Corporate VC",
      "other": "Other"
    },
    "investmentStages": {
      "idea": "Idea",
      "mvp": "MVP",
      "early_traction": "Early Traction",
      "seed": "Seed",
      "series_a": "Series A",
      "series_b": "Series B",
      "growth": "Growth"
    },
    "verticals": {
      "ai": "AI/ML",
      "saas": "SaaS",
      "fintech": "Fintech",
      "biotech": "Biotech",
      "dev_tool": "Developer Tools",
      "social_media": "Social Media",
      "e_commerce": "E-commerce",
      "edutech": "EdTech",
      "enterprise": "Enterprise",
      "robotics": "Robotics",
      "hardware": "Hardware",
      "healthcare": "Healthcare",
      "proptech": "PropTech",
      "cleantech": "CleanTech",
      "mobility": "Mobility",
      "consumer": "Consumer",
      "blockchain": "Blockchain",
      "cybersecurity": "Cybersecurity",
      "entertainment": "Entertainment"
    },
    "geographyOptions": {
      "north_america": "North America",
      "europe": "Europe",
      "china_mainland": "China Mainland",
      "greater_china": "Greater China",
      "pan_asia": "Pan Asia"
    },
    "checkSizeOptions": {
      "< $50k": "< $50k",
      "$50k - $250k": "$50k - $250k",
      "$250k - $1M": "$250k - $1M",
      "> $1M": "> $1M"
    },
    "submit": "Submit Application",
    "submitting": "Submitting...",
    "required": "Required"
  },
  "successModal": {
    "title": "Application Submitted!",
    "founderMessage": "Thank you for submitting your founder application. We're excited to learn more about your startup!",
    "investorMessage": "Thank you for joining our investor network. We're excited to connect you with breakthrough companies!",
    "nextSteps": "What happens next?",
    "step1": "Our team will review your application within 2-3 business days",
    "step2": "We'll reach out to schedule a brief introductory call",
    "step3": "You'll gain access to our platform and network",
    "close": "Got it!",
    "backToHome": "Back to Home"
  },
  "roleSelection": {
    "title": "Choose Your Role",
    "subtitle": "Select the option that best describes you to get started",
    "founder": {
      "title": "I'm a Founder",
      "description": "Looking for investors and funding opportunities",
      "feature1": "Connect with investors",
      "feature2": "Showcase your startup",
      "feature3": "Access funding opportunities",
      "button": "Join as Founder"
    },
    "investor": {
      "title": "I'm an Investor",
      "description": "Looking for investment opportunities and startups",
      "feature1": "Discover quality startups",
      "feature2": "Access deal flow",
      "feature3": "Connect with founders",
      "button": "Join as Investor"
    }
  },
  "about": {
    "title": "About Veridian Vista",
    "subtitle": "We're on a mission to democratize investment intelligence through cutting-edge AI technology, helping investors discover the next generation of breakthrough companies.",
    "ourStory": {
      "title": "Our Story",
      "paragraph1": "Founded in 2025, Veridian Vista emerged from a simple observation: the most promising early-stage investments often remain hidden in the noise of today's information-saturated market. Traditional investment discovery methods were failing to identify these \"green gems\" - the high-potential opportunities that could deliver exceptional returns.",
      "paragraph2": "Our founding team, comprising former investment professionals and AI researchers, recognized that advanced data science and machine learning could transform how investors discover and evaluate opportunities. We built Veridian Vista to bridge this gap, creating an intelligent platform that turns market complexity into competitive advantage.",
      "paragraph3": "Today, we serve forward-thinking investors across the globe, from boutique venture capital firms to family offices and institutional investors, all united by the desire to discover the next breakthrough companies before they become obvious to everyone else."
    },
    "ourValues": {
      "title": "Our Values",
      "innovation": {
        "title": "Innovation",
        "description": "We continuously push the boundaries of what's possible with AI and data science in investment discovery."
      },
      "transparency": {
        "title": "Transparency",
        "description": "We believe in clear, explainable AI that helps investors understand not just what to invest in, but why."
      },
      "impact": {
        "title": "Impact",
        "description": "We're committed to democratizing access to high-quality investment intelligence for investors of all sizes."
      }
    },
    "leadership": {
      "title": "Leadership Team",
      "janeSmith": {
        "name": "Jane Smith",
        "title": "CEO & Co-Founder",
        "description": "Former partner at Sequoia Capital with 15+ years in venture capital and early-stage investing."
      },
      "michaelChen": {
        "name": "Michael Chen",
        "title": "CTO & Co-Founder",
        "description": "Former AI research scientist at DeepMind with expertise in machine learning and data science."
      },
      "emilyPark": {
        "name": "Emily Park",
        "title": "VP of Product",
        "description": "Former product leader at Bloomberg Terminal with deep expertise in financial data platforms."
      }
    }
  }
};
